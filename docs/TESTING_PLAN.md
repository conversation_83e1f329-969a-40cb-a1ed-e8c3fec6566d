# 浏览器插件项目详细测试计划

## 📋 项目概况

**项目名称**: 服务运营工具集合 (fwyy-tools)  
**核心功能**: 多工具集成的浏览器扩展，支持popup和newtab两种展示模式  
**关键特性**: 页面跳转逻辑、工具注册系统、跨页面数据传递  

## 🎯 测试策略概述

按照**Web端优先**的测试策略，确保基础逻辑正确后再进行插件环境测试。

### 测试优先级安排
1. **第一优先级**: Web端功能验证（独立环境测试）
2. **第二优先级**: 插件环境集成测试（扩展API测试）  
3. **第三优先级**: 端到端测试（完整用户流程）

---

## 🔬 第一阶段：Web端功能验证

### 1.1 测试环境搭建

#### 独立Web测试页面
```bash
# 测试文件已创建在
tests/web-standalone/
├── browser-api-mock.ts          # 浏览器API模拟器
├── popup-simulator.html         # Popup界面模拟器
├── newtab-simulator.html        # NewTab界面模拟器
└── README.md                    # 详细使用说明
```

#### 核心测试文件
```bash
tests/core/
├── tool-manager.test.ts         # 页面跳转逻辑测试
├── data-transfer.test.ts        # 数据传递机制测试
└── tool-implementations.test.ts # 工具类功能测试
```

### 1.2 测试执行步骤

#### 步骤1: 运行单元测试
```bash
# 运行所有Web端测试
npm run test:run

# 运行特定测试套件
npx vitest run tests/core/tool-manager.test.ts
npx vitest run tests/core/data-transfer.test.ts
npx vitest run tests/tools/tool-implementations.test.ts

# 生成覆盖率报告
npm run test:coverage
```

#### 步骤2: 使用可视化模拟器
```bash
# 打开Popup模拟器
open tests/web-standalone/popup-simulator.html

# 测试流程：
# 1. 点击"初始化工具"
# 2. 测试popup工具执行
# 3. 测试newtab工具跳转
# 4. 验证数据传递
```

#### 步骤3: 验证标准
- ✅ 所有单元测试通过率 > 95%
- ✅ 代码覆盖率 > 90%
- ✅ 无严重错误或警告
- ✅ 可视化模拟器功能正常

### 1.3 核心测试用例

#### 页面跳转逻辑测试
```typescript
describe('ToolManager页面跳转逻辑', () => {
  it('应该在popup模式下直接执行工具', async () => {
    const popupTool = new MockTool('xuid', 'XUID切换助手', 'popup');
    await toolManager.executeTool(popupTool);
    
    expect(popupTool.action).toHaveBeenCalled();
    expect(mockBrowser.tabs.create).not.toHaveBeenCalled();
  });

  it('应该在newtab模式下跳转到新标签页', async () => {
    const newtabTool = new MockTool('api-diff', 'API Diff工具', 'newtab');
    await toolManager.executeTool(newtabTool);
    
    expect(mockBrowser.storage.local.set).toHaveBeenCalledWith({
      'newtab-tool-launch': expect.objectContaining({
        toolId: 'api-diff'
      })
    });
    expect(mockBrowser.tabs.create).toHaveBeenCalled();
  });
});
```

#### 数据传递机制测试
```typescript
describe('NewTab数据传递机制', () => {
  it('应该正确构造NewTabLaunchData', () => {
    const launchData = DataTransferManager.createLaunchData('test-tool', { key: 'value' });
    
    expect(launchData.toolId).toBe('test-tool');
    expect(launchData.data).toEqual({ key: 'value' });
    expect(launchData.source).toBe('popup');
  });

  it('应该正确保存和读取启动数据', async () => {
    const originalData = DataTransferManager.createLaunchData('test-tool', { key: 'value' });
    
    await DataTransferManager.saveLaunchData(originalData);
    const loadedData = await DataTransferManager.loadLaunchData();
    
    expect(loadedData).toEqual(originalData);
  });
});
```

---

## 🔌 第二阶段：插件环境集成测试

### 2.1 扩展构建和加载

#### 构建测试版本
```bash
# 构建Chrome版本
npm run build

# 构建Firefox版本  
npm run build:firefox

# 验证构建产物
ls -la .output/chrome-mv3/
ls -la .output/firefox-mv2/
```

#### 浏览器加载步骤
1. **Chrome**: 
   - 打开 `chrome://extensions/`
   - 开启开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择 `.output/chrome-mv3` 目录

2. **Firefox**: 
   - 打开 `about:debugging`
   - 点击"此Firefox"
   - 点击"临时载入附加组件"
   - 选择 `.output/firefox-mv2/manifest.json`

### 2.2 插件API集成测试

#### 手动测试步骤
```bash
# 测试清单
□ 扩展图标显示正常
□ 点击图标打开popup
□ popup界面渲染正确
□ 工具列表显示完整
□ popup工具执行正常
□ newtab工具跳转成功
□ 数据传递完整
□ 错误处理正确
□ 通知显示正常
□ 存储功能正常
```

#### 自动化集成测试
```bash
# 创建集成测试文件
tests/integration/
├── storage-api.test.ts          # Storage API集成测试
├── tabs-api.test.ts             # Tabs API集成测试
├── cross-page-communication.test.ts # 跨页面通信测试
└── extension-lifecycle.test.ts   # 扩展生命周期测试

# 运行集成测试
npm run test:integration
```

### 2.3 跨页面通信验证

#### 测试场景
1. **Popup → NewTab数据传递**
   - 在popup中点击newtab工具
   - 验证Storage中保存了正确的启动数据
   - 验证新标签页正确接收数据
   - 验证数据清理机制

2. **错误处理流程**
   - Storage API失败时的回退机制
   - Tabs API失败时的错误提示
   - 网络异常时的处理

3. **并发访问测试**
   - 多个popup同时操作
   - 数据竞争处理
   - 状态同步验证

---

## 🎭 第三阶段：端到端测试

### 3.1 完整用户流程测试

#### 标准用户流程
```bash
# 测试流程
1. 用户安装扩展
2. 点击扩展图标
3. 浏览工具列表
4. 点击popup工具（如XUID切换助手）
5. 验证工具在popup中正确执行
6. 点击newtab工具（如API Diff工具）
7. 验证跳转到新标签页
8. 验证工具在newtab中正确执行
9. 验证数据传递完整性
10. 测试错误处理场景
```

#### 自动化E2E测试
```javascript
// tests/e2e/user-workflow.test.js
describe('完整用户流程', () => {
  it('用户应该能够完成从安装到使用的完整流程', async () => {
    // 1. 验证扩展安装
    await verifyExtensionInstalled();
    
    // 2. 打开popup
    await openExtensionPopup();
    
    // 3. 测试popup工具
    await clickTool('xuid');
    await verifyToolExecuted();
    
    // 4. 测试newtab工具
    await clickTool('api-diff');
    await verifyNewTabOpened();
    await verifyToolLoadedInNewTab();
  });
});
```

### 3.2 浏览器兼容性测试

#### 测试矩阵
| 浏览器 | 版本 | 状态 | 备注 |
|--------|------|------|------|
| Chrome | 120+ | ✅ 支持 | 主要目标浏览器 |
| Chrome | 119 | ✅ 支持 | 前一版本兼容性 |
| Firefox | ESR | ⚠️ 部分支持 | WebExtensions API |
| Firefox | 121+ | ✅ 支持 | 最新稳定版 |
| Edge | 120+ | ✅ 支持 | Chromium内核 |

#### 兼容性测试步骤
```bash
# 每个浏览器的测试步骤
1. 构建对应版本的扩展包
2. 加载到目标浏览器
3. 执行核心功能测试
4. 记录兼容性问题
5. 验证修复方案
```

---

## 📊 测试数据和预期结果

### 测试数据集

#### 标准工具测试数据
```javascript
const testTools = [
  {
    id: 'xuid',
    name: 'XUID切换助手',
    displayMode: 'popup',
    expectedBehavior: 'popup执行'
  },
  {
    id: 'api-diff',
    name: 'API Diff工具',
    displayMode: 'newtab',
    newtabData: { config: 'test' },
    expectedBehavior: 'newtab跳转'
  }
];
```

#### 边界测试数据
```javascript
const edgeCaseData = [
  {
    scenario: '大数据量传递',
    data: { largeArray: new Array(10000).fill('test') }
  },
  {
    scenario: '特殊字符处理',
    data: { special: '中文测试 🚀 "quotes" \'apostrophes\'' }
  },
  {
    scenario: '空数据处理',
    data: null
  }
];
```

### 预期结果标准

#### 功能性测试标准
- ✅ **popup工具**: 在popup环境中正确执行，不触发页面跳转
- ✅ **newtab工具**: 正确跳转到新标签页，数据传递完整
- ✅ **错误处理**: 显示适当的错误信息，提供回退方案
- ✅ **数据完整性**: NewTabLaunchData结构正确，无数据丢失

#### 性能测试标准
- ✅ **跳转速度**: popup到newtab跳转 < 1秒
- ✅ **内存使用**: 扩展内存占用 < 50MB
- ✅ **响应时间**: 工具执行响应 < 500ms

#### 兼容性测试标准
- ✅ **Chrome**: 所有功能正常，无控制台错误
- ✅ **Firefox**: 核心功能正常，API兼容性良好
- ✅ **Edge**: 基础功能正常

---

## 🔄 从Web测试到插件测试的过渡方案

### 过渡步骤

#### 第1步: Web测试完成验证
```bash
# 确保Web测试全部通过
npm run test:run
npm run test:coverage

# 验证覆盖率达标（>90%）
echo "检查测试覆盖率是否达标"
```

#### 第2步: 构建插件版本
```bash
# 构建测试版本
npm run build

# 验证构建产物
ls -la .output/chrome-mv3/
```

#### 第3步: 插件环境测试
```bash
# 手动加载扩展到浏览器
# 执行手动测试清单
# 运行自动化集成测试（如果有）
```

#### 第4步: 问题修复和重测
- 发现问题 → 修复代码 → 重新构建 → 重新测试
- 确保每个阶段都达到通过标准

---

## 🛠️ 测试工具和脚本

### 一键测试脚本
```bash
#!/bin/bash
# scripts/run-all-tests.sh

echo "🚀 开始执行完整测试流程..."

# 第一阶段：Web端测试
echo "📱 执行Web端测试..."
npm run test:run
if [ $? -ne 0 ]; then
  echo "❌ Web端测试失败，停止执行"
  exit 1
fi

# 第二阶段：构建和验证
echo "🔧 构建插件..."
npm run build

echo "✅ 测试流程完成！请手动验证插件功能"
```

### 测试报告模板
```markdown
## 测试执行报告

### Web端测试结果
- 单元测试通过率: XX%
- 代码覆盖率: XX%
- 发现问题: X个
- 修复状态: 已修复/待修复

### 插件环境测试结果
- 手动测试通过率: XX%
- API兼容性: 正常/异常
- 跨页面通信: 正常/异常

### 浏览器兼容性测试结果
- Chrome: ✅/❌
- Firefox: ✅/❌  
- Edge: ✅/❌

### 总体评估
- 测试状态: 通过/失败
- 发布建议: 可发布/需修复
```

---

## 📚 相关文档

- [Web端测试详细说明](../tests/web-standalone/README.md)
- [Vitest配置文档](../vitest.config.ts)
- [项目架构文档](../docs/newtab-jump-design.md)
- [工具开发指南](../README.md)

---

## ✅ 测试完成检查清单

### Web端测试
- [ ] 所有单元测试通过
- [ ] 代码覆盖率 > 90%
- [ ] 可视化模拟器功能正常
- [ ] 错误处理测试通过

### 插件环境测试
- [ ] 扩展成功加载到Chrome
- [ ] 扩展成功加载到Firefox
- [ ] Popup界面正常显示
- [ ] 工具执行功能正常
- [ ] 页面跳转逻辑正确
- [ ] 数据传递完整

### 端到端测试
- [ ] 完整用户流程测试通过
- [ ] 多浏览器兼容性验证
- [ ] 性能指标达标
- [ ] 错误场景处理正确

**测试计划状态**: 🚧 实施中 / ✅ 已完成 / ❌ 需修复
