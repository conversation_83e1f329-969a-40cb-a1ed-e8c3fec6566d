# E2E测试执行指南

## 🎯 测试目标

使用Playwright进行浏览器扩展的端到端测试，验证：
- 扩展在Chrome/Firefox中的正确加载和初始化
- Popup界面的用户交互功能
- NewTab页面的跳转和数据传递
- 完整的用户工作流程

## 🚀 快速开始

### 第1步：安装Playwright依赖

```bash
# 安装Playwright测试框架
npm install @playwright/test

# 安装浏览器（Chrome, Firefox, Safari）
npx playwright install

# 安装系统依赖（Linux用户）
npx playwright install-deps
```

### 第2步：构建扩展

```bash
# 构建Chrome扩展
npm run build

# 构建Firefox扩展
npm run build:firefox

# 验证构建产物
ls -la .output/chrome-mv3/
ls -la .output/firefox-mv2/
```

### 第3步：运行E2E测试

```bash
# 运行所有E2E测试
npm run test:e2e

# 运行有头模式测试（可视化）
npm run test:e2e:headed

# 运行调试模式
npm run test:e2e:debug

# 只运行Chrome扩展测试
npm run test:e2e:chrome

# 只运行Firefox扩展测试
npm run test:e2e:firefox

# 查看测试报告
npm run test:e2e:report
```

## 📋 测试套件详解

### 1. 扩展加载测试 (`extension-loading.test.ts`)

**测试内容：**
- ✅ 扩展在Chrome中正确加载
- ✅ 扩展在Firefox中正确加载
- ✅ 扩展图标和基本信息显示
- ✅ Manifest配置验证
- ✅ 背景脚本初始化
- ✅ 权限配置检查
- ✅ 资源文件加载验证
- ✅ 内容安全策略检查

**关键验证点：**
```typescript
// 验证扩展ID格式
expect(extensionId).toMatch(/^[a-z]{32}$/);

// 验证页面标题
await expect(page).toHaveTitle(/服务运营工具集合/);

// 验证权限配置
const hasStoragePermission = await page.evaluate(() => {
  return typeof chrome.storage !== 'undefined';
});
expect(hasStoragePermission).toBe(true);
```

### 2. Popup交互测试 (`popup-interactions.test.ts`)

**测试内容：**
- ✅ Popup界面正确显示
- ✅ 工具列表渲染
- ✅ Popup/NewTab工具区分
- ✅ Popup模式工具执行
- ✅ 告警解析器功能
- ✅ 工具搜索和筛选
- ✅ 错误处理
- ✅ 键盘快捷键
- ✅ 响应式布局

**关键验证点：**
```typescript
// 验证工具卡片结构
for (const card of toolCards) {
  await expect(card.locator('.tool-icon')).toBeVisible();
  await expect(card.locator('.tool-name')).toBeVisible();
  await expect(card.locator('.tool-description')).toBeVisible();
}

// 验证模态框功能
await xuidTool.click();
await expect(popupPage.locator('.modal')).toBeVisible();
```

### 3. NewTab跳转测试 (`newtab-navigation.test.ts`)

**测试内容：**
- ✅ API Diff工具跳转
- ✅ NewTab示例工具跳转
- ✅ 启动数据传递验证
- ✅ 数据清理机制
- ✅ 全屏布局验证
- ✅ 工具界面交互
- ✅ 导航控制
- ✅ 并发跳转处理
- ✅ 错误场景处理
- ✅ 性能监控

**关键验证点：**
```typescript
// 验证页面跳转
const newPagePromise = context.waitForEvent('page');
await apiDiffTool.click();
const newtabPage = await newPagePromise;

// 验证数据传递
const launchData = await newtabPage.evaluate(async () => {
  const result = await chrome.storage.local.get('newtab-tool-launch');
  return result['newtab-tool-launch'];
});
expect(launchData.toolId).toBe('api-diff');
```

### 4. 完整用户流程测试 (`user-workflows.test.ts`)

**测试内容：**
- ✅ 完整Popup工具使用流程
- ✅ 完整NewTab工具使用流程
- ✅ 错误处理用户体验
- ✅ 多工具连续使用
- ✅ 工具发现和学习
- ✅ 性能和响应性
- ✅ 可访问性支持

**关键验证点：**
```typescript
// 完整流程验证
console.log('步骤1: 验证扩展安装状态');
console.log('步骤2: 打开扩展popup');
console.log('步骤3: 浏览可用工具');
console.log('步骤4: 执行XUID切换助手');
console.log('步骤5: 与工具进行交互');
console.log('步骤6: 关闭工具');
```

## 🔧 测试配置详解

### Playwright配置 (`playwright.config.ts`)

```typescript
export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: false, // 扩展测试需要串行执行
  workers: 1, // 使用单worker
  
  projects: [
    {
      name: 'chrome-extension',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-extensions-except=' + extensionPath,
            '--load-extension=' + extensionPath
          ],
          headless: false
        }
      }
    }
  ]
});
```

### 测试数据配置

```typescript
export const TEST_DATA = {
  tools: {
    popup: [
      { id: 'xuid', name: 'XUID切换助手', icon: '🕹️' },
      { id: 'alert-parser', name: '告警解析器', icon: '🚨' }
    ],
    newtab: [
      { id: 'api-diff', name: 'API Diff工具', icon: '🔍' },
      { id: 'example-newtab', name: 'NewTab示例', icon: '🚀' }
    ]
  },
  
  timeouts: {
    extensionLoad: 5000,
    popupOpen: 3000,
    toolExecution: 10000
  }
};
```

## 📊 测试结果分析

### 成功的测试输出

```
Running 25 tests using 1 worker

✓ extension-loading.test.ts (8 tests)
  ✓ 应该成功加载Chrome扩展
  ✓ 应该正确显示扩展图标和基本信息
  ✓ 应该正确加载扩展的manifest配置
  ✓ 应该正确初始化扩展的背景脚本
  ✓ 应该正确处理扩展的内容安全策略
  ✓ 应该正确加载所有必需的资源文件
  ✓ 应该正确处理扩展更新和重载
  ✓ 应该正确处理扩展权限请求

✓ popup-interactions.test.ts (12 tests)
✓ newtab-navigation.test.ts (10 tests)
✓ user-workflows.test.ts (6 tests)

Passed: 25/25 (100%)
Duration: 2m 15s
```

### 测试报告

#### HTML报告
- 详细的测试结果展示
- 失败测试的截图和视频
- 性能指标分析
- 浏览器兼容性报告

#### JSON报告
```json
{
  "stats": {
    "total": 25,
    "passed": 25,
    "failed": 0,
    "skipped": 0,
    "duration": 135000
  },
  "browsers": ["Chrome"],
  "coverage": {
    "extensionLoading": 100,
    "popupInteractions": 95,
    "newtabNavigation": 90,
    "userWorkflows": 85
  }
}
```

## 🐛 常见问题解决

### Q1: 扩展加载失败
**症状**: 测试报错"Extension not found"
**解决方案**:
```bash
# 1. 确保扩展已构建
npm run build

# 2. 检查构建产物
ls -la .output/chrome-mv3/manifest.json

# 3. 验证manifest.json格式
cat .output/chrome-mv3/manifest.json | jq .
```

### Q2: 测试超时
**症状**: 测试在等待元素时超时
**解决方案**:
```typescript
// 增加特定操作的超时时间
await expect(page.locator('.modal')).toBeVisible({ timeout: 10000 });

// 或在配置中全局设置
export default defineConfig({
  timeout: 60000,
  expect: { timeout: 15000 }
});
```

### Q3: 权限被拒绝
**症状**: Chrome扩展权限相关错误
**解决方案**:
```typescript
// 在测试配置中添加权限
use: {
  permissions: ['storage', 'tabs', 'notifications'],
  launchOptions: {
    args: [
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  }
}
```

### Q4: Firefox测试失败
**症状**: Firefox扩展无法加载
**解决方案**:
```bash
# 1. 安装Firefox
sudo apt-get install firefox  # Linux
brew install firefox          # macOS

# 2. 检查Firefox版本
firefox --version

# 3. 构建Firefox扩展
npm run build:firefox

# 4. 验证Firefox扩展
ls -la .output/firefox-mv2/
```

### Q5: 页面元素找不到
**症状**: 选择器无法找到元素
**解决方案**:
```typescript
// 使用更具体的选择器
await page.locator('[data-tool-id="xuid"]').click();

// 等待元素出现
await page.waitForSelector('.tool-card', { timeout: 5000 });

// 使用文本内容定位
await page.locator('text=XUID切换助手').click();
```

## 📈 性能监控

### 加载时间监控
```typescript
test('应该在合理时间内加载', async () => {
  const startTime = Date.now();
  
  const popupPage = await openExtensionPopup(page, extensionId);
  await popupPage.waitForLoadState('domcontentloaded');
  
  const loadTime = Date.now() - startTime;
  expect(loadTime).toBeLessThan(3000); // 3秒内加载完成
});
```

### 内存使用监控
```typescript
test('应该控制内存使用', async () => {
  const metrics = await page.evaluate(() => {
    return {
      usedJSHeapSize: performance.memory?.usedJSHeapSize || 0,
      totalJSHeapSize: performance.memory?.totalJSHeapSize || 0
    };
  });
  
  // 验证内存使用合理
  expect(metrics.usedJSHeapSize).toBeLessThan(50 * 1024 * 1024); // 50MB
});
```

## 🔄 持续集成

### GitHub Actions配置
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Build extension
        run: npm run build
      
      - name: Run E2E tests
        run: npm run test:e2e:chrome
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: test-results/
```

## 📋 测试完成检查清单

### 环境准备
- [ ] Playwright已安装
- [ ] 浏览器已安装
- [ ] 扩展已构建
- [ ] 测试配置正确

### 扩展加载测试
- [ ] Chrome扩展成功加载
- [ ] Firefox扩展成功加载（可选）
- [ ] 扩展权限配置正确
- [ ] 资源文件加载正常

### 功能测试
- [ ] Popup界面正常显示
- [ ] 工具列表正确渲染
- [ ] Popup工具正常执行
- [ ] NewTab工具正确跳转
- [ ] 数据传递完整

### 用户体验测试
- [ ] 完整用户流程顺畅
- [ ] 错误处理用户友好
- [ ] 性能指标达标
- [ ] 响应式设计正常

### 报告和文档
- [ ] 测试报告生成
- [ ] 失败测试有截图
- [ ] 性能指标记录
- [ ] 问题修复建议

**准备好开始E2E测试了！运行 `npm run test:e2e` 开始验证您的浏览器扩展功能。**
