# Web端测试执行指南

## 🎯 测试目标

验证浏览器插件的核心功能在独立Web环境中的正确性，包括：
- 页面跳转逻辑（popup → newtab）
- 数据传递机制
- 工具注册和执行流程
- 错误处理和边界情况

## 🚀 快速开始

### 第1步：运行所有单元测试

```bash
# 运行所有测试
npm run test:run

# 或者使用监视模式（推荐开发时使用）
npm run test:watch
```

**预期输出示例：**
```
✓ tests/core/tool-manager.test.ts (8)
  ✓ ToolManager页面跳转逻辑测试 (8)
    ✓ executeTool方法 - popup模式 (2)
    ✓ executeTool方法 - newtab模式 (3)
    ✓ executeTool方法 - 错误处理 (3)

✓ tests/core/data-transfer.test.ts (12)
  ✓ NewTab数据传递机制测试 (12)

✓ tests/tools/tool-implementations.test.ts (15)
  ✓ 工具类实现测试 (15)

Test Files  3 passed (3)
Tests       35 passed (35)
Start at    14:30:25
Duration    2.34s
```

### 第2步：生成覆盖率报告

```bash
# 生成详细的覆盖率报告
npm run test:coverage
```

**预期覆盖率目标：**
- **语句覆盖率**: > 90%
- **分支覆盖率**: > 85%
- **函数覆盖率**: > 95%
- **行覆盖率**: > 90%

### 第3步：查看HTML覆盖率报告

```bash
# 在浏览器中打开覆盖率报告
open coverage/index.html
```

## 📋 详细测试说明

### 核心功能测试

#### 1. ToolManager页面跳转逻辑测试
**文件**: `tests/core/tool-manager.test.ts`

**测试内容：**
- ✅ popup模式工具直接执行
- ✅ newtab模式工具跳转到新标签页
- ✅ 工具不存在时的错误处理
- ✅ Storage API失败时的回退机制
- ✅ Tabs API失败时的错误处理

**关键验证点：**
```typescript
// popup模式验证
expect(popupTool.action).toHaveBeenCalled();
expect(mockBrowser.tabs.create).not.toHaveBeenCalled();

// newtab模式验证
expect(mockBrowser.storage.local.set).toHaveBeenCalledWith({
  'newtab-tool-launch': expect.objectContaining({
    toolId: 'api-diff'
  })
});
expect(mockBrowser.tabs.create).toHaveBeenCalled();
```

#### 2. 数据传递机制测试
**文件**: `tests/core/data-transfer.test.ts`

**测试内容：**
- ✅ NewTabLaunchData正确构造
- ✅ Storage保存和读取机制
- ✅ 数据验证和完整性检查
- ✅ 大数据量处理
- ✅ 特殊字符和边界情况

**关键验证点：**
```typescript
// 数据结构验证
expect(launchData.toolId).toBe('test-tool');
expect(launchData.data).toEqual({ key: 'value' });
expect(launchData.source).toBe('popup');

// 数据传递验证
const loadedData = await DataTransferManager.loadLaunchData();
expect(loadedData).toEqual(originalData);
```

#### 3. 工具实现测试
**文件**: `tests/tools/tool-implementations.test.ts`

**测试内容：**
- ✅ XuidTool界面创建和初始化
- ✅ AlertParserTool解析逻辑
- ✅ ExampleNewTabTool生命周期管理
- ✅ 基类功能验证

**关键验证点：**
```typescript
// 工具界面创建验证
expect(mockDocument.body.appendChild).toHaveBeenCalled();
const modalCall = mockDocument.body.appendChild.mock.calls[0][0];
expect(modalCall.innerHTML).toContain('XUID切换助手');

// NewTab工具环境检查
expect(newTabTool.isInNewTabEnvironment()).toBe(true);
```

## 🔍 测试结果分析

### 成功标准

#### ✅ 全部通过
```
Test Files  3 passed (3)
Tests       35 passed (35)
Coverage    92.5% (Statements: 185/200)
```

**下一步**: 可以进入第二阶段插件环境测试

#### ⚠️ 部分失败
```
Test Files  2 passed, 1 failed (3)
Tests       30 passed, 5 failed (35)
```

**处理方案**:
1. 查看具体失败的测试用例
2. 检查错误信息和堆栈跟踪
3. 修复相关代码
4. 重新运行测试

#### ❌ 大量失败
```
Test Files  1 passed, 2 failed (3)
Tests       15 passed, 20 failed (35)
```

**处理方案**:
1. 检查测试环境配置
2. 验证依赖安装是否完整
3. 查看浏览器API mock是否正确设置
4. 逐个修复失败的测试

### 覆盖率分析

#### 📊 覆盖率报告解读
```
File                    | % Stmts | % Branch | % Funcs | % Lines
------------------------|---------|----------|---------|--------
All files              |   92.5  |   87.3   |   96.2  |   91.8
 core/                 |   94.1  |   89.5   |   97.8  |   93.2
  tool-manager.ts      |   95.2  |   91.2   |   98.5  |   94.8
  data-transfer.ts     |   93.1  |   87.8   |   97.1  |   91.6
 tools/                |   90.8  |   85.1   |   94.6  |   90.4
```

**分析要点**:
- **Stmts (语句)**: 代码中每个语句的执行情况
- **Branch (分支)**: if/else、switch等分支的覆盖情况
- **Funcs (函数)**: 函数调用的覆盖情况
- **Lines (行)**: 代码行的执行情况

#### 🎯 提升覆盖率
如果覆盖率不达标，重点关注：
1. **未覆盖的分支**: 添加错误场景测试
2. **未调用的函数**: 确保所有公共方法都有测试
3. **未执行的语句**: 检查是否有死代码

## 🛠️ 常见问题解决

### Q1: 测试运行失败，提示"browser is not defined"
**解决方案**:
```bash
# 检查setup.ts是否正确配置
cat tests/setup.ts | grep "browser"

# 确保在测试文件中导入了mock
import { setupBrowserMock } from '../web-standalone/browser-api-mock';
```

### Q2: 某些测试超时
**解决方案**:
```typescript
// 在vitest.config.ts中调整超时设置
export default defineConfig({
  test: {
    testTimeout: 15000, // 增加到15秒
    hookTimeout: 15000
  }
});
```

### Q3: Mock函数没有按预期工作
**解决方案**:
```typescript
// 在每个测试前重置mock
beforeEach(() => {
  vi.clearAllMocks();
  setupBrowserMock();
});
```

### Q4: 覆盖率报告显示不准确
**解决方案**:
```bash
# 清理之前的覆盖率数据
rm -rf coverage/

# 重新生成覆盖率报告
npm run test:coverage
```

## 📈 测试优化建议

### 1. 并行测试
```bash
# 使用并行模式加速测试
npm run test:run -- --reporter=verbose --threads
```

### 2. 特定测试运行
```bash
# 只运行特定文件的测试
npx vitest run tests/core/tool-manager.test.ts

# 只运行匹配模式的测试
npx vitest run -t "popup模式"
```

### 3. 调试模式
```bash
# 使用调试模式运行测试
npx vitest run --reporter=verbose --no-coverage
```

## 🔄 下一步行动

### ✅ Web端测试通过后
1. **构建插件**: `npm run build`
2. **加载到浏览器**: 开发者模式加载扩展
3. **手动验证**: 测试popup和newtab功能
4. **进入第二阶段**: 插件环境集成测试

### 📋 测试完成检查清单
- [ ] 所有单元测试通过（35/35）
- [ ] 代码覆盖率 > 90%
- [ ] 无严重错误或警告
- [ ] 核心功能验证完成
- [ ] 错误处理测试通过
- [ ] 边界情况测试通过

**准备好开始测试了！运行 `npm run test:run` 开始验证您的插件核心功能。**
