/**
 * Popup交互测试
 * 测试扩展popup界面的用户交互和功能
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';
import { getExtensionId, openExtensionPopup, verifyToolsList, TEST_DATA } from './playwright.config';

test.describe('Popup交互测试', () => {
  let context: BrowserContext;
  let page: Page;
  let popupPage: Page;
  let extensionId: string;

  test.beforeEach(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
    
    // 获取扩展ID并打开popup
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    popupPage = await openExtensionPopup(page, extensionId);
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('应该正确显示popup界面', async () => {
    // 验证popup页面标题
    await expect(popupPage).toHaveTitle(/服务运营工具集合/);
    
    // 验证主要UI元素
    await expect(popupPage.locator('.popup-container')).toBeVisible();
    await expect(popupPage.locator('.tool-grid')).toBeVisible();
    
    // 验证页面尺寸符合popup规范
    const viewport = popupPage.viewportSize();
    expect(viewport?.width).toBeLessThanOrEqual(800);
    expect(viewport?.height).toBeLessThanOrEqual(600);
  });

  test('应该正确渲染工具列表', async () => {
    // 等待工具列表加载
    await popupPage.waitForSelector('.tool-card', { timeout: 5000 });
    
    // 验证工具卡片数量
    const toolCards = await popupPage.locator('.tool-card').all();
    expect(toolCards.length).toBeGreaterThan(0);
    
    // 验证每个工具卡片的基本结构
    for (const card of toolCards) {
      await expect(card.locator('.tool-icon')).toBeVisible();
      await expect(card.locator('.tool-name')).toBeVisible();
      await expect(card.locator('.tool-description')).toBeVisible();
    }
    
    // 验证特定工具的存在
    await expect(popupPage.locator('[data-tool-id="xuid"]')).toBeVisible();
    await expect(popupPage.locator('[data-tool-id="alert-parser"]')).toBeVisible();
    await expect(popupPage.locator('[data-tool-id="api-diff"]')).toBeVisible();
  });

  test('应该正确区分popup和newtab工具', async () => {
    // 验证popup工具没有newtab标识
    const xuidTool = popupPage.locator('[data-tool-id="xuid"]');
    await expect(xuidTool.locator('.newtab-badge')).not.toBeVisible();
    
    // 验证newtab工具有newtab标识
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await expect(apiDiffTool.locator('.newtab-badge')).toBeVisible();
    
    // 验证newtab工具的特殊样式
    await expect(apiDiffTool).toHaveClass(/newtab/);
  });

  test('应该正确执行popup模式工具', async () => {
    // 点击XUID切换助手（popup工具）
    const xuidTool = popupPage.locator('[data-tool-id="xuid"]');
    await xuidTool.click();
    
    // 验证模态框出现
    await expect(popupPage.locator('.modal')).toBeVisible({ timeout: 5000 });
    
    // 验证模态框内容
    const modal = popupPage.locator('.modal');
    await expect(modal.locator('.modal-title')).toContainText('XUID切换助手');
    await expect(modal.locator('.modal-content')).toBeVisible();
    
    // 验证关闭按钮功能
    const closeButton = modal.locator('.close-btn');
    await closeButton.click();
    await expect(modal).not.toBeVisible();
  });

  test('应该正确处理告警解析器工具', async () => {
    // 点击告警解析器
    const alertParserTool = popupPage.locator('[data-tool-id="alert-parser"]');
    await alertParserTool.click();
    
    // 验证告警解析器模态框
    await expect(popupPage.locator('.alert-parser-modal')).toBeVisible({ timeout: 5000 });
    
    // 验证输入区域
    const textArea = popupPage.locator('#alert-input');
    await expect(textArea).toBeVisible();
    await expect(textArea).toHaveAttribute('placeholder', /告警信息/);
    
    // 测试输入功能
    const testAlert = '测试告警信息：服务器CPU使用率过高';
    await textArea.fill(testAlert);
    
    // 验证解析按钮
    const parseButton = popupPage.locator('#parse-btn');
    await expect(parseButton).toBeVisible();
    await expect(parseButton).toBeEnabled();
    
    // 点击解析按钮
    await parseButton.click();
    
    // 验证解析结果区域出现
    await expect(popupPage.locator('#result-section')).toBeVisible({ timeout: 3000 });
  });

  test('应该正确处理newtab工具的跳转逻辑', async () => {
    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');
    
    // 点击API Diff工具（newtab工具）
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 验证通知显示
    await expect(popupPage.locator('.notification')).toBeVisible({ timeout: 3000 });
    const notification = popupPage.locator('.notification');
    await expect(notification).toContainText('正在新标签页中打开');
    
    // 等待新页面打开
    const newPage = await newPagePromise;
    await newPage.waitForLoadState('domcontentloaded');
    
    // 验证新页面是newtab页面
    expect(newPage.url()).toContain('newtab.html');
    
    // 验证新页面内容
    await expect(newPage.locator('.newtab-container')).toBeVisible();
    await expect(newPage.locator('.tool-title')).toContainText('API Diff工具');
    
    await newPage.close();
  });

  test('应该正确处理工具搜索功能', async () => {
    // 验证搜索框存在
    const searchInput = popupPage.locator('#tool-search');
    if (await searchInput.isVisible()) {
      // 测试搜索功能
      await searchInput.fill('XUID');
      
      // 验证搜索结果
      await expect(popupPage.locator('[data-tool-id="xuid"]')).toBeVisible();
      await expect(popupPage.locator('[data-tool-id="alert-parser"]')).not.toBeVisible();
      
      // 清空搜索
      await searchInput.clear();
      
      // 验证所有工具重新显示
      await expect(popupPage.locator('[data-tool-id="xuid"]')).toBeVisible();
      await expect(popupPage.locator('[data-tool-id="alert-parser"]')).toBeVisible();
    }
  });

  test('应该正确处理工具分类筛选', async () => {
    // 验证分类按钮存在
    const categoryButtons = popupPage.locator('.category-btn');
    const buttonCount = await categoryButtons.count();
    
    if (buttonCount > 0) {
      // 点击第一个分类按钮
      await categoryButtons.first().click();
      
      // 验证分类筛选效果
      const visibleTools = await popupPage.locator('.tool-card:visible').count();
      expect(visibleTools).toBeGreaterThan(0);
      
      // 点击"全部"分类
      const allCategoryBtn = popupPage.locator('.category-btn').filter({ hasText: '全部' });
      if (await allCategoryBtn.isVisible()) {
        await allCategoryBtn.click();
        
        // 验证所有工具重新显示
        const allTools = await popupPage.locator('.tool-card:visible').count();
        expect(allTools).toBeGreaterThanOrEqual(visibleTools);
      }
    }
  });

  test('应该正确处理工具设置功能', async () => {
    // 查找工具设置按钮
    const settingsButton = popupPage.locator('.tool-settings-btn').first();
    
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      
      // 验证设置模态框
      await expect(popupPage.locator('.tool-settings-modal')).toBeVisible({ timeout: 3000 });
      
      // 验证设置选项
      const settingsModal = popupPage.locator('.tool-settings-modal');
      await expect(settingsModal.locator('.setting-item')).toBeVisible();
      
      // 关闭设置模态框
      const closeBtn = settingsModal.locator('.close-btn');
      await closeBtn.click();
      await expect(settingsModal).not.toBeVisible();
    }
  });

  test('应该正确处理错误情况', async () => {
    // 模拟网络错误情况
    await popupPage.route('**/*', route => {
      if (route.request().url().includes('api')) {
        route.abort();
      } else {
        route.continue();
      }
    });
    
    // 尝试执行需要网络请求的工具
    const taskListTool = popupPage.locator('[data-tool-id="task-list"]');
    if (await taskListTool.isVisible()) {
      await taskListTool.click();
      
      // 验证错误处理
      await expect(popupPage.locator('.error-message')).toBeVisible({ timeout: 5000 });
    }
  });

  test('应该正确处理键盘快捷键', async () => {
    // 测试ESC键关闭模态框
    const xuidTool = popupPage.locator('[data-tool-id="xuid"]');
    await xuidTool.click();
    
    await expect(popupPage.locator('.modal')).toBeVisible();
    
    // 按ESC键
    await popupPage.keyboard.press('Escape');
    
    // 验证模态框关闭
    await expect(popupPage.locator('.modal')).not.toBeVisible();
  });

  test('应该正确处理工具拖拽排序', async () => {
    test.skip(true, '拖拽功能需要特殊的测试环境');
    
    // 获取第一个和第二个工具卡片
    const firstTool = popupPage.locator('.tool-card').first();
    const secondTool = popupPage.locator('.tool-card').nth(1);
    
    // 获取初始位置
    const firstToolId = await firstTool.getAttribute('data-tool-id');
    const secondToolId = await secondTool.getAttribute('data-tool-id');
    
    // 执行拖拽操作
    await firstTool.dragTo(secondTool);
    
    // 验证位置交换
    const newFirstTool = popupPage.locator('.tool-card').first();
    const newFirstToolId = await newFirstTool.getAttribute('data-tool-id');
    
    expect(newFirstToolId).toBe(secondToolId);
  });

  test('应该正确处理响应式布局', async () => {
    // 测试不同的viewport尺寸
    const viewports = [
      { width: 400, height: 600 },
      { width: 350, height: 500 },
      { width: 300, height: 400 }
    ];
    
    for (const viewport of viewports) {
      await popupPage.setViewportSize(viewport);
      
      // 验证工具网格仍然可见和可用
      await expect(popupPage.locator('.tool-grid')).toBeVisible();
      
      // 验证工具卡片仍然可点击
      const toolCards = await popupPage.locator('.tool-card').all();
      expect(toolCards.length).toBeGreaterThan(0);
      
      // 验证没有水平滚动条
      const hasHorizontalScroll = await popupPage.evaluate(() => {
        return document.body.scrollWidth > document.body.clientWidth;
      });
      expect(hasHorizontalScroll).toBe(false);
    }
  });
});
