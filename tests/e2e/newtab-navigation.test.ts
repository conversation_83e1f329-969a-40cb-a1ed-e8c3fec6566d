/**
 * NewTab跳转测试
 * 测试从popup到newtab的页面跳转和数据传递
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';
import { getExtensionId, openExtensionPopup, TEST_DATA } from './playwright.config';

test.describe('NewTab跳转测试', () => {
  let context: BrowserContext;
  let page: Page;
  let popupPage: Page;
  let extensionId: string;

  test.beforeEach(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
    
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    popupPage = await openExtensionPopup(page, extensionId);
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('应该正确跳转到API Diff工具的newtab页面', async () => {
    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');
    
    // 点击API Diff工具
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 等待新页面打开
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证URL正确
    expect(newtabPage.url()).toContain('newtab.html');
    expect(newtabPage.url()).toContain(extensionId);
    
    // 验证页面标题
    await expect(newtabPage).toHaveTitle(/API Diff工具/);
    
    // 验证newtab容器
    await expect(newtabPage.locator('.newtab-container')).toBeVisible();
    
    // 验证工具特定内容
    await expect(newtabPage.locator('.tool-title')).toContainText('API Diff工具');
    await expect(newtabPage.locator('.tool-icon')).toContainText('🔍');
    
    await newtabPage.close();
  });

  test('应该正确跳转到NewTab示例工具页面', async () => {
    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');
    
    // 点击NewTab示例工具
    const exampleTool = popupPage.locator('[data-tool-id="example-newtab"]');
    await exampleTool.click();
    
    // 等待新页面打开
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证页面内容
    await expect(newtabPage.locator('.tool-title')).toContainText('NewTab示例');
    await expect(newtabPage.locator('.tool-description')).toBeVisible();
    
    // 验证示例工具的特定功能
    await expect(newtabPage.locator('.example-interface')).toBeVisible();
    
    await newtabPage.close();
  });

  test('应该正确传递工具启动数据', async () => {
    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');
    
    // 点击API Diff工具
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 等待新页面打开
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证启动数据是否正确传递
    const launchData = await newtabPage.evaluate(async () => {
      // 检查Chrome Storage中的启动数据
      try {
        const result = await chrome.storage.local.get('newtab-tool-launch');
        return result['newtab-tool-launch'];
      } catch (error) {
        return null;
      }
    });
    
    // 验证启动数据结构
    expect(launchData).toBeTruthy();
    expect(launchData.toolId).toBe('api-diff');
    expect(launchData.source).toBe('popup');
    expect(launchData.timestamp).toBeTypeOf('number');
    expect(launchData.data).toBeDefined();
    
    await newtabPage.close();
  });

  test('应该正确清理启动数据', async () => {
    // 监听新页面创建
    const newPagePromise = context.waitForEvent('page');
    
    // 点击API Diff工具
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 等待新页面打开
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 等待工具初始化完成
    await newtabPage.waitForTimeout(2000);
    
    // 验证启动数据已被清理
    const remainingData = await newtabPage.evaluate(async () => {
      try {
        const result = await chrome.storage.local.get('newtab-tool-launch');
        return result['newtab-tool-launch'];
      } catch (error) {
        return 'error';
      }
    });
    
    // 启动数据应该已被清理
    expect(remainingData).toBeNull();
    
    await newtabPage.close();
  });

  test('应该正确处理newtab页面的全屏布局', async () => {
    const newPagePromise = context.waitForEvent('page');
    
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证全屏布局
    const containerHeight = await newtabPage.locator('.newtab-container').evaluate(el => {
      return window.getComputedStyle(el).height;
    });
    
    // 容器应该占满视口高度
    expect(containerHeight).toBe('100vh');
    
    // 验证响应式设计
    const viewport = newtabPage.viewportSize();
    expect(viewport?.width).toBeGreaterThan(800);
    expect(viewport?.height).toBeGreaterThan(600);
    
    await newtabPage.close();
  });

  test('应该正确处理newtab页面的工具界面', async () => {
    const newPagePromise = context.waitForEvent('page');
    
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证API Diff工具的特定界面元素
    await expect(newtabPage.locator('.api-diff-interface')).toBeVisible();
    
    // 验证输入区域
    const primaryUrlInput = newtabPage.locator('#primary-url');
    const secondaryUrlInput = newtabPage.locator('#secondary-url');
    
    if (await primaryUrlInput.isVisible()) {
      await expect(primaryUrlInput).toBeVisible();
      await expect(secondaryUrlInput).toBeVisible();
      
      // 测试输入功能
      await primaryUrlInput.fill('https://api.example.com/v1/test');
      await secondaryUrlInput.fill('https://api-test.example.com/v1/test');
      
      // 验证执行按钮
      const executeButton = newtabPage.locator('#execute-diff');
      await expect(executeButton).toBeVisible();
      await expect(executeButton).toBeEnabled();
    }
    
    await newtabPage.close();
  });

  test('应该正确处理newtab页面的导航控制', async () => {
    const newPagePromise = context.waitForEvent('page');
    
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证导航栏
    await expect(newtabPage.locator('.newtab-header')).toBeVisible();
    
    // 验证返回按钮
    const backButton = newtabPage.locator('.back-btn');
    if (await backButton.isVisible()) {
      await backButton.click();
      
      // 验证是否返回到工具选择界面
      await expect(newtabPage.locator('.tool-selector')).toBeVisible();
    }
    
    // 验证关闭按钮
    const closeButton = newtabPage.locator('.close-btn');
    if (await closeButton.isVisible()) {
      // 注意：实际点击会关闭页面，这里只验证按钮存在
      await expect(closeButton).toBeVisible();
    }
    
    await newtabPage.close();
  });

  test('应该正确处理多个newtab工具的并发跳转', async () => {
    // 创建多个新页面的监听器
    const newPagePromises = [
      context.waitForEvent('page'),
      context.waitForEvent('page')
    ];
    
    // 快速点击两个newtab工具
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    const exampleTool = popupPage.locator('[data-tool-id="example-newtab"]');
    
    await apiDiffTool.click();
    await exampleTool.click();
    
    // 等待两个页面都打开
    const [newtabPage1, newtabPage2] = await Promise.all(newPagePromises);
    
    await newtabPage1.waitForLoadState('domcontentloaded');
    await newtabPage2.waitForLoadState('domcontentloaded');
    
    // 验证两个页面都正确加载
    const page1Title = await newtabPage1.title();
    const page2Title = await newtabPage2.title();
    
    expect([page1Title, page2Title]).toContain('API Diff工具');
    expect([page1Title, page2Title]).toContain('NewTab示例');
    
    await newtabPage1.close();
    await newtabPage2.close();
  });

  test('应该正确处理newtab页面的错误情况', async () => {
    // 模拟Storage API失败
    await popupPage.addInitScript(() => {
      const originalSet = chrome.storage.local.set;
      chrome.storage.local.set = () => Promise.reject(new Error('Storage quota exceeded'));
    });
    
    // 尝试跳转到newtab
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 验证错误处理 - 应该显示错误通知
    await expect(popupPage.locator('.notification.error')).toBeVisible({ timeout: 5000 });
    
    // 验证错误消息内容
    const errorNotification = popupPage.locator('.notification.error');
    await expect(errorNotification).toContainText('打开新标签页失败');
  });

  test('应该正确处理newtab页面的键盘快捷键', async () => {
    const newPagePromise = context.waitForEvent('page');
    
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 测试Ctrl+R刷新功能
    await newtabPage.keyboard.press('Control+r');
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证页面仍然正常
    await expect(newtabPage.locator('.newtab-container')).toBeVisible();
    
    // 测试ESC键功能（如果有）
    await newtabPage.keyboard.press('Escape');
    
    await newtabPage.close();
  });

  test('应该正确处理newtab页面的状态持久化', async () => {
    const newPagePromise = context.waitForEvent('page');
    
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 在工具中输入一些数据
    const primaryUrlInput = newtabPage.locator('#primary-url');
    if (await primaryUrlInput.isVisible()) {
      const testUrl = 'https://api.example.com/test';
      await primaryUrlInput.fill(testUrl);
      
      // 刷新页面
      await newtabPage.reload();
      await newtabPage.waitForLoadState('domcontentloaded');
      
      // 验证数据是否持久化（如果工具支持）
      const savedValue = await primaryUrlInput.inputValue();
      // 注意：这取决于工具是否实现了状态持久化
      console.log('保存的值:', savedValue);
    }
    
    await newtabPage.close();
  });

  test('应该正确处理newtab页面的性能', async () => {
    const newPagePromise = context.waitForEvent('page');

    // 记录开始时间
    const startTime = Date.now();

    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();

    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');

    // 记录加载完成时间
    const loadTime = Date.now() - startTime;

    // 验证加载时间合理（应该在3秒内）
    expect(loadTime).toBeLessThan(3000);

    // 验证页面性能指标
    const performanceMetrics = await newtabPage.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart
      };
    });

    expect(performanceMetrics.domContentLoaded).toBeLessThan(1000);

    await newtabPage.close();
  });

  test('应该正确处理newtab页面的工具选择器', async () => {
    // 直接访问newtab页面（没有启动数据）
    const newtabUrl = `chrome-extension://${extensionId}/newtab.html`;
    const newtabPage = await context.newPage();
    await newtabPage.goto(newtabUrl);

    // 验证工具选择器显示
    await expect(newtabPage.locator('.tool-selector')).toBeVisible();

    // 验证工具列表
    const toolCards = await newtabPage.locator('.tool-card').all();
    expect(toolCards.length).toBeGreaterThan(0);

    // 点击一个工具
    const firstTool = newtabPage.locator('.tool-card').first();
    await firstTool.click();

    // 验证工具启动
    await expect(newtabPage.locator('.tool-container')).toBeVisible();

    await newtabPage.close();
  });
});
