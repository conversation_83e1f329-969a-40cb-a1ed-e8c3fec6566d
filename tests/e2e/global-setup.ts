/**
 * Playwright全局设置 - 扩展构建和准备
 */

import { chromium, firefox, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 开始E2E测试全局设置...');
  
  try {
    // 1. 构建扩展
    console.log('📦 构建浏览器扩展...');
    await buildExtensions();
    
    // 2. 验证构建产物
    console.log('✅ 验证构建产物...');
    await verifyBuildArtifacts();
    
    // 3. 准备测试环境
    console.log('🔧 准备测试环境...');
    await setupTestEnvironment();
    
    // 4. 预热浏览器
    console.log('🌡️ 预热浏览器环境...');
    await warmupBrowsers();
    
    console.log('✅ E2E测试全局设置完成');
    
  } catch (error) {
    console.error('❌ E2E测试全局设置失败:', error);
    throw error;
  }
}

// 构建扩展
async function buildExtensions(): Promise<void> {
  try {
    // 构建Chrome版本
    console.log('  📦 构建Chrome扩展...');
    execSync('npm run build', { 
      stdio: 'pipe',
      cwd: path.resolve(__dirname, '../..'),
      timeout: 120000 
    });
    
    // 构建Firefox版本
    console.log('  📦 构建Firefox扩展...');
    execSync('npm run build:firefox', { 
      stdio: 'pipe',
      cwd: path.resolve(__dirname, '../..'),
      timeout: 120000 
    });
    
    console.log('  ✅ 扩展构建完成');
    
  } catch (error) {
    console.error('  ❌ 扩展构建失败:', error);
    throw new Error('扩展构建失败，无法进行E2E测试');
  }
}

// 验证构建产物
async function verifyBuildArtifacts(): Promise<void> {
  const chromeExtensionPath = path.resolve(__dirname, '../../.output/chrome-mv3');
  const firefoxExtensionPath = path.resolve(__dirname, '../../.output/firefox-mv2');
  
  // 验证Chrome扩展
  if (!fs.existsSync(chromeExtensionPath)) {
    throw new Error('Chrome扩展构建产物不存在');
  }
  
  const chromeManifest = path.join(chromeExtensionPath, 'manifest.json');
  if (!fs.existsSync(chromeManifest)) {
    throw new Error('Chrome扩展manifest.json不存在');
  }
  
  // 验证关键文件
  const chromeRequiredFiles = [
    'popup.html',
    'newtab.html',
    'background.js'
  ];
  
  for (const file of chromeRequiredFiles) {
    const filePath = path.join(chromeExtensionPath, file);
    if (!fs.existsSync(filePath)) {
      console.warn(`  ⚠️ Chrome扩展缺少文件: ${file}`);
    }
  }
  
  // 验证Firefox扩展
  if (!fs.existsSync(firefoxExtensionPath)) {
    console.warn('  ⚠️ Firefox扩展构建产物不存在，跳过Firefox测试');
    return;
  }
  
  const firefoxManifest = path.join(firefoxExtensionPath, 'manifest.json');
  if (!fs.existsSync(firefoxManifest)) {
    console.warn('  ⚠️ Firefox扩展manifest.json不存在');
  }
  
  console.log('  ✅ 构建产物验证完成');
}

// 设置测试环境
async function setupTestEnvironment(): Promise<void> {
  // 创建测试结果目录
  const testResultsDir = path.resolve(__dirname, '../../test-results');
  const e2eReportDir = path.join(testResultsDir, 'e2e-report');
  const e2eArtifactsDir = path.join(testResultsDir, 'e2e-artifacts');
  
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  if (!fs.existsSync(e2eReportDir)) {
    fs.mkdirSync(e2eReportDir, { recursive: true });
  }
  
  if (!fs.existsSync(e2eArtifactsDir)) {
    fs.mkdirSync(e2eArtifactsDir, { recursive: true });
  }
  
  // 创建测试数据文件
  const testDataPath = path.join(testResultsDir, 'e2e-test-data.json');
  const testData = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'test',
    extensions: {
      chrome: {
        path: path.resolve(__dirname, '../../.output/chrome-mv3'),
        manifest: readManifest('chrome')
      },
      firefox: {
        path: path.resolve(__dirname, '../../.output/firefox-mv2'),
        manifest: readManifest('firefox')
      }
    }
  };
  
  fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
  
  console.log('  ✅ 测试环境设置完成');
}

// 读取manifest文件
function readManifest(browser: 'chrome' | 'firefox'): any {
  try {
    const manifestPath = browser === 'chrome' 
      ? path.resolve(__dirname, '../../.output/chrome-mv3/manifest.json')
      : path.resolve(__dirname, '../../.output/firefox-mv2/manifest.json');
    
    if (fs.existsSync(manifestPath)) {
      return JSON.parse(fs.readFileSync(manifestPath, 'utf-8'));
    }
    return null;
  } catch (error) {
    console.warn(`  ⚠️ 无法读取${browser}的manifest.json:`, error);
    return null;
  }
}

// 预热浏览器
async function warmupBrowsers(): Promise<void> {
  try {
    // 预热Chrome
    console.log('  🌡️ 预热Chrome浏览器...');
    const chromeBrowser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const chromePage = await chromeBrowser.newPage();
    await chromePage.goto('about:blank');
    await chromePage.close();
    await chromeBrowser.close();
    
    // 预热Firefox
    console.log('  🌡️ 预热Firefox浏览器...');
    try {
      const firefoxBrowser = await firefox.launch({ headless: true });
      const firefoxPage = await firefoxBrowser.newPage();
      await firefoxPage.goto('about:blank');
      await firefoxPage.close();
      await firefoxBrowser.close();
    } catch (error) {
      console.warn('  ⚠️ Firefox预热失败，可能未安装Firefox');
    }
    
    console.log('  ✅ 浏览器预热完成');
    
  } catch (error) {
    console.warn('  ⚠️ 浏览器预热失败:', error);
    // 预热失败不应该阻止测试
  }
}

// 检查系统依赖
async function checkSystemDependencies(): Promise<void> {
  console.log('  🔍 检查系统依赖...');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  console.log(`    Node.js版本: ${nodeVersion}`);
  
  // 检查npm版本
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf-8' }).trim();
    console.log(`    npm版本: ${npmVersion}`);
  } catch (error) {
    console.warn('    ⚠️ 无法获取npm版本');
  }
  
  // 检查Chrome是否可用
  try {
    const chromeVersion = execSync('google-chrome --version', { encoding: 'utf-8' }).trim();
    console.log(`    Chrome版本: ${chromeVersion}`);
  } catch (error) {
    console.warn('    ⚠️ Chrome未安装或不可用');
  }
  
  // 检查Firefox是否可用
  try {
    const firefoxVersion = execSync('firefox --version', { encoding: 'utf-8' }).trim();
    console.log(`    Firefox版本: ${firefoxVersion}`);
  } catch (error) {
    console.warn('    ⚠️ Firefox未安装或不可用');
  }
  
  console.log('  ✅ 系统依赖检查完成');
}

// 清理旧的测试数据
async function cleanupOldTestData(): Promise<void> {
  console.log('  🧹 清理旧的测试数据...');
  
  const testResultsDir = path.resolve(__dirname, '../../test-results');
  
  if (fs.existsSync(testResultsDir)) {
    // 清理旧的截图和视频
    const artifactsDir = path.join(testResultsDir, 'e2e-artifacts');
    if (fs.existsSync(artifactsDir)) {
      const files = fs.readdirSync(artifactsDir);
      for (const file of files) {
        if (file.endsWith('.png') || file.endsWith('.webm')) {
          fs.unlinkSync(path.join(artifactsDir, file));
        }
      }
    }
  }
  
  console.log('  ✅ 旧测试数据清理完成');
}

export default globalSetup;
