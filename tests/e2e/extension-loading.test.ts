/**
 * 扩展加载和初始化测试
 * 验证扩展在不同浏览器中的正确加载和基础功能
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';
import { getExtensionId, waitForExtensionLoad, TEST_DATA } from './playwright.config';

test.describe('扩展加载和初始化测试', () => {
  let context: BrowserContext;
  let page: Page;
  let extensionId: string;

  test.beforeEach(async ({ browser }) => {
    // 创建新的浏览器上下文
    context = await browser.newContext();
    page = await context.newPage();
    
    // 等待扩展加载
    await waitForExtensionLoad(page);
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('应该成功加载Chrome扩展', async () => {
    test.skip(process.env.BROWSER !== 'chrome', '仅在Chrome中运行');
    
    // 获取扩展ID
    extensionId = await getExtensionId(page, 'chrome');
    expect(extensionId).toBeTruthy();
    expect(extensionId).toMatch(/^[a-z]{32}$/);
    
    // 验证扩展页面可访问
    const extensionUrl = `chrome-extension://${extensionId}/popup.html`;
    await page.goto(extensionUrl);
    
    // 验证页面加载成功
    await expect(page).toHaveTitle(/服务运营工具集合/);
    
    // 验证基础DOM结构
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('.tool-grid')).toBeVisible();
  });

  test('应该成功加载Firefox扩展', async () => {
    test.skip(process.env.BROWSER !== 'firefox', '仅在Firefox中运行');
    
    // Firefox扩展加载验证
    await page.goto('about:debugging#/runtime/this-firefox');
    
    // 验证扩展在调试页面中可见
    const extensionCard = page.locator('text=服务运营工具集合');
    await expect(extensionCard).toBeVisible();
    
    // 获取扩展内部UUID
    const extensionUuid = await page.evaluate(() => {
      const cards = document.querySelectorAll('.debug-target-item');
      for (const card of cards) {
        const name = card.querySelector('.debug-target-item__name')?.textContent;
        if (name?.includes('服务运营工具集合')) {
          const inspectBtn = card.querySelector('[data-qa="temporary-extension-inspect-button"]');
          return inspectBtn?.getAttribute('data-extension-id');
        }
      }
      return null;
    });
    
    expect(extensionUuid).toBeTruthy();
  });

  test('应该正确显示扩展图标和基本信息', async () => {
    // 访问扩展管理页面
    if (process.env.BROWSER === 'chrome') {
      await page.goto('chrome://extensions/');
      
      // 查找我们的扩展
      const extensionCard = page.locator('extensions-item').filter({
        has: page.locator('text=服务运营工具集合')
      });
      
      await expect(extensionCard).toBeVisible();
      
      // 验证扩展信息
      const extensionName = extensionCard.locator('#name');
      await expect(extensionName).toHaveText('服务运营工具集合');
      
      const extensionDescription = extensionCard.locator('#description');
      await expect(extensionDescription).toContainText('浏览器扩展工具集合');
      
      // 验证扩展已启用
      const toggleButton = extensionCard.locator('#enableToggle');
      await expect(toggleButton).toBeChecked();
      
    } else if (process.env.BROWSER === 'firefox') {
      await page.goto('about:addons');
      
      // 在Firefox附加组件页面查找扩展
      const extensionItem = page.locator('.addon-card').filter({
        has: page.locator('text=服务运营工具集合')
      });
      
      await expect(extensionItem).toBeVisible();
    }
  });

  test('应该正确加载扩展的manifest配置', async () => {
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    // 访问扩展的popup页面来验证manifest配置
    const popupUrl = `chrome-extension://${extensionId}/popup.html`;
    await page.goto(popupUrl);
    
    // 验证页面标题（来自manifest）
    await expect(page).toHaveTitle(/服务运营工具集合/);
    
    // 验证权限是否正确配置（通过检查API可用性）
    const hasStoragePermission = await page.evaluate(() => {
      return typeof chrome !== 'undefined' && 
             typeof chrome.storage !== 'undefined' &&
             typeof chrome.storage.local !== 'undefined';
    });
    expect(hasStoragePermission).toBe(true);
    
    const hasTabsPermission = await page.evaluate(() => {
      return typeof chrome !== 'undefined' && 
             typeof chrome.tabs !== 'undefined';
    });
    expect(hasTabsPermission).toBe(true);
    
    const hasNotificationsPermission = await page.evaluate(() => {
      return typeof chrome !== 'undefined' && 
             typeof chrome.notifications !== 'undefined';
    });
    expect(hasNotificationsPermission).toBe(true);
  });

  test('应该正确初始化扩展的背景脚本', async () => {
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    // 验证背景脚本是否运行
    const backgroundPageUrl = `chrome-extension://${extensionId}/_generated_background_page.html`;
    
    try {
      await page.goto(backgroundPageUrl);
      
      // 验证背景脚本的基本功能
      const backgroundScriptLoaded = await page.evaluate(() => {
        return typeof chrome !== 'undefined' && 
               typeof chrome.runtime !== 'undefined';
      });
      expect(backgroundScriptLoaded).toBe(true);
      
    } catch (error) {
      // 某些浏览器可能不允许直接访问背景页面
      console.log('无法直接访问背景页面，这是正常的');
    }
  });

  test('应该正确处理扩展的内容安全策略', async () => {
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    const popupUrl = `chrome-extension://${extensionId}/popup.html`;
    await page.goto(popupUrl);
    
    // 验证页面加载没有CSP错误
    const cspErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error' && msg.text().includes('Content Security Policy')) {
        cspErrors.push(msg.text());
      }
    });
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 验证没有CSP错误
    expect(cspErrors).toHaveLength(0);
  });

  test('应该正确加载所有必需的资源文件', async () => {
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    const popupUrl = `chrome-extension://${extensionId}/popup.html`;
    
    // 监听网络请求
    const failedRequests: string[] = [];
    
    page.on('response', (response) => {
      if (!response.ok() && response.url().startsWith('chrome-extension://')) {
        failedRequests.push(`${response.status()} - ${response.url()}`);
      }
    });
    
    await page.goto(popupUrl);
    await page.waitForLoadState('networkidle');
    
    // 验证没有资源加载失败
    expect(failedRequests).toHaveLength(0);
    
    // 验证关键CSS文件已加载
    const stylesLoaded = await page.evaluate(() => {
      const stylesheets = Array.from(document.styleSheets);
      return stylesheets.length > 0;
    });
    expect(stylesLoaded).toBe(true);
    
    // 验证关键JavaScript文件已加载
    const scriptsLoaded = await page.evaluate(() => {
      const scripts = Array.from(document.scripts);
      return scripts.some(script => script.src.includes('main.js') || script.src.includes('popup.js'));
    });
    expect(scriptsLoaded).toBe(true);
  });

  test('应该正确处理扩展更新和重载', async () => {
    test.skip(process.env.CI === 'true', '在CI环境中跳过扩展重载测试');
    
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    if (process.env.BROWSER === 'chrome') {
      // 访问扩展管理页面
      await page.goto('chrome://extensions/');
      
      // 找到我们的扩展
      const extensionCard = page.locator('extensions-item').filter({
        has: page.locator('text=服务运营工具集合')
      });
      
      // 点击重载按钮
      const reloadButton = extensionCard.locator('#reloadButton');
      await reloadButton.click();
      
      // 等待重载完成
      await page.waitForTimeout(2000);
      
      // 验证扩展仍然可用
      const popupUrl = `chrome-extension://${extensionId}/popup.html`;
      await page.goto(popupUrl);
      
      await expect(page.locator('.tool-grid')).toBeVisible();
    }
  });

  test('应该正确处理扩展权限请求', async () => {
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
    
    const popupUrl = `chrome-extension://${extensionId}/popup.html`;
    await page.goto(popupUrl);
    
    // 测试存储权限
    const storageTest = await page.evaluate(async () => {
      try {
        await chrome.storage.local.set({ test: 'value' });
        const result = await chrome.storage.local.get('test');
        await chrome.storage.local.remove('test');
        return result.test === 'value';
      } catch (error) {
        return false;
      }
    });
    expect(storageTest).toBe(true);
    
    // 测试标签页权限
    const tabsTest = await page.evaluate(async () => {
      try {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        return Array.isArray(tabs);
      } catch (error) {
        return false;
      }
    });
    expect(tabsTest).toBe(true);
  });
});
