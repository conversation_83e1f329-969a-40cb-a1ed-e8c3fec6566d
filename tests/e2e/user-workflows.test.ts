/**
 * 完整用户流程测试
 * 测试从扩展安装到工具使用的完整用户体验
 */

import { test, expect, Page, BrowserContext } from '@playwright/test';
import { getExtensionId, openExtensionPopup, TEST_DATA } from './playwright.config';

test.describe('完整用户流程测试', () => {
  let context: BrowserContext;
  let page: Page;
  let extensionId: string;

  test.beforeEach(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
    
    extensionId = await getExtensionId(page, process.env.BROWSER || 'chrome');
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('完整的用户工作流程：从安装到使用popup工具', async () => {
    // 步骤1: 验证扩展已安装
    console.log('步骤1: 验证扩展安装状态');
    expect(extensionId).toBeTruthy();
    
    // 步骤2: 打开popup
    console.log('步骤2: 打开扩展popup');
    const popupPage = await openExtensionPopup(page, extensionId);
    await expect(popupPage.locator('.tool-grid')).toBeVisible();
    
    // 步骤3: 浏览工具列表
    console.log('步骤3: 浏览可用工具');
    const toolCards = await popupPage.locator('.tool-card').all();
    expect(toolCards.length).toBeGreaterThan(0);
    
    // 验证工具信息显示
    for (const card of toolCards.slice(0, 3)) { // 只检查前3个工具
      await expect(card.locator('.tool-name')).toBeVisible();
      await expect(card.locator('.tool-description')).toBeVisible();
      await expect(card.locator('.tool-icon')).toBeVisible();
    }
    
    // 步骤4: 选择并执行XUID切换助手
    console.log('步骤4: 执行XUID切换助手');
    const xuidTool = popupPage.locator('[data-tool-id="xuid"]');
    await xuidTool.click();
    
    // 验证工具模态框打开
    await expect(popupPage.locator('.modal')).toBeVisible({ timeout: 5000 });
    
    // 步骤5: 与工具交互
    console.log('步骤5: 与工具进行交互');
    const modal = popupPage.locator('.modal');
    await expect(modal.locator('.modal-title')).toContainText('XUID');
    
    // 如果有输入框，进行输入测试
    const xuidInput = modal.locator('#xuid-input');
    if (await xuidInput.isVisible()) {
      await xuidInput.fill('12345678');
      
      // 点击切换按钮
      const switchButton = modal.locator('#switch-btn');
      if (await switchButton.isVisible()) {
        await switchButton.click();
        
        // 验证操作反馈
        await expect(popupPage.locator('.notification')).toBeVisible({ timeout: 3000 });
      }
    }
    
    // 步骤6: 关闭工具
    console.log('步骤6: 关闭工具');
    const closeButton = modal.locator('.close-btn');
    await closeButton.click();
    await expect(modal).not.toBeVisible();
    
    console.log('✅ Popup工具流程测试完成');
  });

  test('完整的用户工作流程：从popup跳转到newtab工具', async () => {
    // 步骤1: 打开popup
    console.log('步骤1: 打开扩展popup');
    const popupPage = await openExtensionPopup(page, extensionId);
    
    // 步骤2: 识别newtab工具
    console.log('步骤2: 识别newtab工具');
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await expect(apiDiffTool).toBeVisible();
    await expect(apiDiffTool.locator('.newtab-badge')).toBeVisible();
    
    // 步骤3: 点击newtab工具
    console.log('步骤3: 点击API Diff工具');
    const newPagePromise = context.waitForEvent('page');
    await apiDiffTool.click();
    
    // 验证跳转提示
    await expect(popupPage.locator('.notification')).toBeVisible({ timeout: 3000 });
    
    // 步骤4: 等待新标签页打开
    console.log('步骤4: 等待新标签页打开');
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    // 验证新标签页内容
    expect(newtabPage.url()).toContain('newtab.html');
    await expect(newtabPage.locator('.newtab-container')).toBeVisible();
    
    // 步骤5: 在newtab中使用工具
    console.log('步骤5: 在newtab中使用API Diff工具');
    await expect(newtabPage.locator('.tool-title')).toContainText('API Diff');
    
    // 如果有工具界面，进行交互测试
    const primaryUrlInput = newtabPage.locator('#primary-url');
    if (await primaryUrlInput.isVisible()) {
      await primaryUrlInput.fill('https://api.example.com/v1/test');
      
      const secondaryUrlInput = newtabPage.locator('#secondary-url');
      await secondaryUrlInput.fill('https://api-test.example.com/v1/test');
      
      // 点击执行按钮
      const executeButton = newtabPage.locator('#execute-diff');
      if (await executeButton.isVisible()) {
        await executeButton.click();
        
        // 验证执行状态
        await expect(newtabPage.locator('.status-panel')).toBeVisible({ timeout: 5000 });
      }
    }
    
    // 步骤6: 验证数据传递
    console.log('步骤6: 验证数据传递完整性');
    const launchData = await newtabPage.evaluate(async () => {
      try {
        const result = await chrome.storage.local.get('newtab-tool-launch');
        return result['newtab-tool-launch'];
      } catch (error) {
        return null;
      }
    });
    
    // 数据应该已被清理，或者包含正确的工具ID
    if (launchData) {
      expect(launchData.toolId).toBe('api-diff');
    }
    
    await newtabPage.close();
    console.log('✅ NewTab工具流程测试完成');
  });

  test('错误处理流程：Storage API失败的用户体验', async () => {
    console.log('测试Storage API失败场景');
    
    const popupPage = await openExtensionPopup(page, extensionId);
    
    // 模拟Storage API失败
    await popupPage.addInitScript(() => {
      const originalSet = chrome.storage.local.set;
      chrome.storage.local.set = () => Promise.reject(new Error('Storage quota exceeded'));
    });
    
    // 尝试使用newtab工具
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    // 验证错误处理
    await expect(popupPage.locator('.notification.error')).toBeVisible({ timeout: 5000 });
    
    // 验证回退机制 - 工具应该在popup中执行
    const errorNotification = popupPage.locator('.notification.error');
    await expect(errorNotification).toContainText('失败');
    
    console.log('✅ Storage错误处理测试完成');
  });

  test('多工具使用流程：连续使用不同类型的工具', async () => {
    console.log('测试连续使用多个工具');
    
    const popupPage = await openExtensionPopup(page, extensionId);
    
    // 第一个工具：XUID切换助手（popup工具）
    console.log('使用第一个工具：XUID切换助手');
    const xuidTool = popupPage.locator('[data-tool-id="xuid"]');
    await xuidTool.click();
    
    await expect(popupPage.locator('.modal')).toBeVisible();
    
    // 关闭第一个工具
    const closeButton = popupPage.locator('.modal .close-btn');
    await closeButton.click();
    await expect(popupPage.locator('.modal')).not.toBeVisible();
    
    // 第二个工具：告警解析器（popup工具）
    console.log('使用第二个工具：告警解析器');
    const alertParserTool = popupPage.locator('[data-tool-id="alert-parser"]');
    await alertParserTool.click();
    
    await expect(popupPage.locator('.alert-parser-modal')).toBeVisible();
    
    // 在告警解析器中输入内容
    const alertInput = popupPage.locator('#alert-input');
    if (await alertInput.isVisible()) {
      await alertInput.fill('测试告警信息');
    }
    
    // 关闭第二个工具
    const alertCloseButton = popupPage.locator('.alert-parser-modal .close-btn');
    await alertCloseButton.click();
    
    // 第三个工具：API Diff（newtab工具）
    console.log('使用第三个工具：API Diff');
    const newPagePromise = context.waitForEvent('page');
    const apiDiffTool = popupPage.locator('[data-tool-id="api-diff"]');
    await apiDiffTool.click();
    
    const newtabPage = await newPagePromise;
    await newtabPage.waitForLoadState('domcontentloaded');
    
    await expect(newtabPage.locator('.tool-title')).toContainText('API Diff');
    
    await newtabPage.close();
    console.log('✅ 多工具使用流程测试完成');
  });

  test('用户体验流程：工具发现和学习', async () => {
    console.log('测试工具发现和学习流程');
    
    const popupPage = await openExtensionPopup(page, extensionId);
    
    // 步骤1: 浏览所有工具
    console.log('步骤1: 浏览工具列表');
    const toolCards = await popupPage.locator('.tool-card').all();
    
    for (let i = 0; i < Math.min(toolCards.length, 3); i++) {
      const card = toolCards[i];
      
      // 悬停查看工具信息
      await card.hover();
      
      // 验证工具信息可见
      await expect(card.locator('.tool-name')).toBeVisible();
      await expect(card.locator('.tool-description')).toBeVisible();
      
      // 检查是否有帮助信息
      const helpIcon = card.locator('.help-icon');
      if (await helpIcon.isVisible()) {
        await helpIcon.click();
        // 验证帮助信息显示
        await expect(popupPage.locator('.help-tooltip')).toBeVisible();
      }
    }
    
    // 步骤2: 使用搜索功能（如果存在）
    console.log('步骤2: 测试搜索功能');
    const searchInput = popupPage.locator('#tool-search');
    if (await searchInput.isVisible()) {
      await searchInput.fill('API');
      
      // 验证搜索结果
      const visibleTools = await popupPage.locator('.tool-card:visible').count();
      expect(visibleTools).toBeGreaterThan(0);
      
      // 清空搜索
      await searchInput.clear();
    }
    
    // 步骤3: 使用分类筛选（如果存在）
    console.log('步骤3: 测试分类筛选');
    const categoryButtons = popupPage.locator('.category-btn');
    const categoryCount = await categoryButtons.count();
    
    if (categoryCount > 0) {
      // 点击第一个分类
      await categoryButtons.first().click();
      
      // 验证筛选效果
      const filteredTools = await popupPage.locator('.tool-card:visible').count();
      expect(filteredTools).toBeGreaterThan(0);
    }
    
    console.log('✅ 工具发现流程测试完成');
  });

  test('性能和响应性流程：快速操作测试', async () => {
    console.log('测试快速操作性能');
    
    const startTime = Date.now();
    
    // 快速打开popup
    const popupPage = await openExtensionPopup(page, extensionId);
    const popupLoadTime = Date.now() - startTime;
    
    // 验证popup加载时间
    expect(popupLoadTime).toBeLessThan(2000);
    
    // 快速点击多个工具
    const toolCards = await popupPage.locator('.tool-card').all();
    
    for (let i = 0; i < Math.min(toolCards.length, 3); i++) {
      const card = toolCards[i];
      const toolId = await card.getAttribute('data-tool-id');
      
      if (toolId && !toolId.includes('newtab')) {
        // 只测试popup工具以避免打开太多标签页
        const clickStart = Date.now();
        await card.click();
        
        // 等待模态框出现
        await expect(popupPage.locator('.modal')).toBeVisible({ timeout: 3000 });
        const responseTime = Date.now() - clickStart;
        
        // 验证响应时间
        expect(responseTime).toBeLessThan(1000);
        
        // 关闭模态框
        const closeButton = popupPage.locator('.modal .close-btn');
        await closeButton.click();
        await expect(popupPage.locator('.modal')).not.toBeVisible();
      }
    }
    
    console.log('✅ 性能测试完成');
  });

  test('可访问性流程：键盘导航和屏幕阅读器支持', async () => {
    console.log('测试可访问性功能');
    
    const popupPage = await openExtensionPopup(page, extensionId);
    
    // 测试Tab键导航
    await popupPage.keyboard.press('Tab');
    
    // 验证焦点管理
    const focusedElement = await popupPage.evaluate(() => {
      return document.activeElement?.tagName;
    });
    
    expect(focusedElement).toBeTruthy();
    
    // 测试Enter键激活
    await popupPage.keyboard.press('Enter');
    
    // 验证是否有适当的响应
    // 这取决于具体的焦点元素
    
    // 测试ESC键关闭
    await popupPage.keyboard.press('Escape');
    
    console.log('✅ 可访问性测试完成');
  });
});
