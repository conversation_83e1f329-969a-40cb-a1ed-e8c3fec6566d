# 浏览器扩展 E2E 测试

使用 Playwright 进行的端到端测试，专门针对浏览器扩展的功能验证。

## 📋 测试覆盖范围

### 1. 扩展加载和初始化测试 (`extension-loading.test.ts`)
- ✅ Chrome/Firefox 扩展正确加载
- ✅ 扩展图标和基本信息显示
- ✅ Manifest 配置验证
- ✅ 背景脚本初始化
- ✅ 权限配置验证
- ✅ 资源文件加载
- ✅ 内容安全策略检查

### 2. Popup 交互测试 (`popup-interactions.test.ts`)
- ✅ Popup 界面正确显示
- ✅ 工具列表渲染
- ✅ Popup/NewTab 工具区分
- ✅ Popup 模式工具执行
- ✅ 工具搜索和筛选
- ✅ 错误处理
- ✅ 键盘快捷键
- ✅ 响应式布局

### 3. NewTab 跳转测试 (`newtab-navigation.test.ts`)
- ✅ API Diff 工具跳转
- ✅ NewTab 示例工具跳转
- ✅ 启动数据传递
- ✅ 数据清理机制
- ✅ 全屏布局验证
- ✅ 工具界面交互
- ✅ 导航控制
- ✅ 并发跳转处理
- ✅ 错误场景处理
- ✅ 性能监控

### 4. 完整用户流程测试 (`user-workflows.test.ts`)
- ✅ 完整 Popup 工具使用流程
- ✅ 完整 NewTab 工具使用流程
- ✅ 错误处理用户体验
- ✅ 多工具连续使用
- ✅ 工具发现和学习
- ✅ 性能和响应性
- ✅ 可访问性支持

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 Playwright
npm install @playwright/test

# 安装浏览器
npx playwright install

# 安装系统依赖（Linux）
npx playwright install-deps
```

### 2. 构建扩展

```bash
# 构建 Chrome 扩展
npm run build

# 构建 Firefox 扩展
npm run build:firefox
```

### 3. 运行测试

```bash
# 运行所有 E2E 测试
npm run test:e2e

# 运行有头模式测试（可视化）
npm run test:e2e:headed

# 运行调试模式
npm run test:e2e:debug

# 只运行 Chrome 扩展测试
npm run test:e2e:chrome

# 只运行 Firefox 扩展测试
npm run test:e2e:firefox

# 查看测试报告
npm run test:e2e:report
```

## 📊 测试配置

### 浏览器配置

#### Chrome 扩展测试
```typescript
{
  name: 'chrome-extension',
  use: {
    ...devices['Desktop Chrome'],
    channel: 'chrome',
    launchOptions: {
      args: [
        '--disable-extensions-except=' + extensionPath,
        '--load-extension=' + extensionPath,
        '--disable-web-security'
      ],
      headless: false
    }
  }
}
```

#### Firefox 扩展测试
```typescript
{
  name: 'firefox-extension',
  use: {
    ...devices['Desktop Firefox'],
    launchOptions: {
      firefoxUserPrefs: {
        'xpinstall.signatures.required': false,
        'extensions.autoDisableScopes': 0
      },
      headless: false
    }
  }
}
```

### 测试数据配置

```typescript
export const TEST_DATA = {
  tools: {
    popup: [
      { id: 'xuid', name: 'XUID切换助手', icon: '🕹️' },
      { id: 'alert-parser', name: '告警解析器', icon: '🚨' }
    ],
    newtab: [
      { id: 'api-diff', name: 'API Diff工具', icon: '🔍' },
      { id: 'example-newtab', name: 'NewTab示例', icon: '🚀' }
    ]
  },
  
  selectors: {
    extensionIcon: '[data-testid="extension-icon"]',
    toolCard: '.tool-card',
    modal: '.modal',
    notification: '.notification'
  },
  
  timeouts: {
    extensionLoad: 5000,
    popupOpen: 3000,
    toolExecution: 10000
  }
};
```

## 🔧 测试工具函数

### 扩展 ID 获取
```typescript
export async function getExtensionId(page: Page, browser: string): Promise<string> {
  if (browser === 'chrome') {
    await page.goto('chrome://extensions/');
    return await page.evaluate(() => {
      // 查找扩展ID的逻辑
    });
  }
}
```

### 打开扩展 Popup
```typescript
export async function openExtensionPopup(page: Page, extensionId: string): Promise<Page> {
  const popupUrl = `chrome-extension://${extensionId}/popup.html`;
  const popupPage = await page.context().newPage();
  await popupPage.goto(popupUrl);
  return popupPage;
}
```

### 验证工具列表
```typescript
export async function verifyToolsList(page: Page, expectedTools: any[]): Promise<boolean> {
  const toolCards = await page.locator('.tool-card').all();
  return toolCards.length === expectedTools.length;
}
```

## 📈 测试报告

### HTML 报告
测试完成后会生成详细的 HTML 报告：
- 测试结果概览
- 失败测试的截图和视频
- 性能指标
- 浏览器兼容性报告

### JSON 报告
结构化的测试结果数据：
```json
{
  "stats": {
    "total": 25,
    "passed": 23,
    "failed": 2,
    "skipped": 0
  },
  "browsers": ["Chrome", "Firefox"],
  "duration": 45000
}
```

### Markdown 报告
自动生成的测试摘要：
- 测试概览
- 功能覆盖率
- 失败测试详情
- 性能指标

## 🐛 常见问题

### Q1: 扩展加载失败
**解决方案**:
```bash
# 确保扩展已构建
npm run build

# 检查构建产物
ls -la .output/chrome-mv3/

# 验证 manifest.json
cat .output/chrome-mv3/manifest.json
```

### Q2: 测试超时
**解决方案**:
```typescript
// 增加超时时间
test.setTimeout(60000);

// 或在配置中设置
export default defineConfig({
  timeout: 60000,
  expect: { timeout: 10000 }
});
```

### Q3: 权限问题
**解决方案**:
```typescript
// 在测试中设置权限
use: {
  permissions: ['storage', 'tabs', 'notifications']
}
```

### Q4: Firefox 扩展测试失败
**解决方案**:
```bash
# 安装 Firefox
sudo apt-get install firefox  # Linux
brew install firefox          # macOS

# 检查 Firefox 版本
firefox --version
```

## 📋 测试检查清单

### 扩展加载测试
- [ ] Chrome 扩展成功加载
- [ ] Firefox 扩展成功加载
- [ ] 扩展图标正确显示
- [ ] Manifest 配置正确
- [ ] 权限配置有效

### 功能测试
- [ ] Popup 界面正常显示
- [ ] 工具列表正确渲染
- [ ] Popup 工具正常执行
- [ ] NewTab 工具正确跳转
- [ ] 数据传递完整

### 用户体验测试
- [ ] 完整用户流程顺畅
- [ ] 错误处理用户友好
- [ ] 性能指标达标
- [ ] 可访问性支持良好

### 兼容性测试
- [ ] Chrome 最新版本兼容
- [ ] Firefox 最新版本兼容
- [ ] 不同屏幕尺寸适配

## 🔄 持续集成

### GitHub Actions 配置
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run build
      - run: npm run test:e2e
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: test-results/
```

## 📚 相关文档

- [Playwright 官方文档](https://playwright.dev/)
- [Chrome 扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [Firefox 扩展开发文档](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions)
- [项目主要测试计划](../../docs/TESTING_PLAN.md)

---

**测试状态**: 🚧 开发中 | ✅ 已完成 | ❌ 需修复
