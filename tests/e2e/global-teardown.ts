/**
 * Playwright全局清理 - 测试结束后的清理工作
 */

import { FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始E2E测试全局清理...');
  
  try {
    // 1. 生成测试报告摘要
    console.log('📊 生成测试报告摘要...');
    await generateTestSummary();
    
    // 2. 清理临时文件
    console.log('🗑️ 清理临时文件...');
    await cleanupTempFiles();
    
    // 3. 归档测试结果
    console.log('📦 归档测试结果...');
    await archiveTestResults();
    
    console.log('✅ E2E测试全局清理完成');
    
  } catch (error) {
    console.error('❌ E2E测试全局清理失败:', error);
    // 清理失败不应该影响测试结果
  }
}

// 生成测试报告摘要
async function generateTestSummary(): Promise<void> {
  try {
    const testResultsDir = path.resolve(__dirname, '../../test-results');
    const resultsFile = path.join(testResultsDir, 'e2e-results.json');
    
    if (!fs.existsSync(resultsFile)) {
      console.log('  ⚠️ 测试结果文件不存在，跳过报告生成');
      return;
    }
    
    const results = JSON.parse(fs.readFileSync(resultsFile, 'utf-8'));
    
    const summary = {
      timestamp: new Date().toISOString(),
      totalTests: results.stats?.total || 0,
      passedTests: results.stats?.passed || 0,
      failedTests: results.stats?.failed || 0,
      skippedTests: results.stats?.skipped || 0,
      duration: results.stats?.duration || 0,
      browsers: extractBrowserInfo(results),
      failedTestDetails: extractFailedTests(results),
      coverage: {
        extensionLoading: calculateCoverage(results, 'extension-loading'),
        popupInteractions: calculateCoverage(results, 'popup-interactions'),
        newtabNavigation: calculateCoverage(results, 'newtab-navigation'),
        userWorkflows: calculateCoverage(results, 'user-workflows')
      }
    };
    
    const summaryFile = path.join(testResultsDir, 'e2e-summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    
    // 生成Markdown报告
    const markdownReport = generateMarkdownReport(summary);
    const markdownFile = path.join(testResultsDir, 'E2E_TEST_REPORT.md');
    fs.writeFileSync(markdownFile, markdownReport);
    
    console.log('  ✅ 测试报告摘要生成完成');
    console.log(`  📊 总测试数: ${summary.totalTests}`);
    console.log(`  ✅ 通过: ${summary.passedTests}`);
    console.log(`  ❌ 失败: ${summary.failedTests}`);
    console.log(`  ⏭️ 跳过: ${summary.skippedTests}`);
    
  } catch (error) {
    console.error('  ❌ 生成测试报告摘要失败:', error);
  }
}

// 提取浏览器信息
function extractBrowserInfo(results: any): any {
  const browsers = new Set();
  
  if (results.suites) {
    results.suites.forEach((suite: any) => {
      if (suite.title.includes('chrome')) {
        browsers.add('Chrome');
      }
      if (suite.title.includes('firefox')) {
        browsers.add('Firefox');
      }
    });
  }
  
  return Array.from(browsers);
}

// 提取失败的测试
function extractFailedTests(results: any): any[] {
  const failedTests: any[] = [];
  
  if (results.suites) {
    results.suites.forEach((suite: any) => {
      if (suite.specs) {
        suite.specs.forEach((spec: any) => {
          if (spec.tests) {
            spec.tests.forEach((test: any) => {
              if (test.results) {
                test.results.forEach((result: any) => {
                  if (result.status === 'failed') {
                    failedTests.push({
                      title: test.title,
                      file: spec.file,
                      error: result.error?.message || 'Unknown error',
                      duration: result.duration
                    });
                  }
                });
              }
            });
          }
        });
      }
    });
  }
  
  return failedTests;
}

// 计算测试覆盖率
function calculateCoverage(results: any, category: string): number {
  let total = 0;
  let passed = 0;
  
  if (results.suites) {
    results.suites.forEach((suite: any) => {
      if (suite.title.includes(category)) {
        if (suite.specs) {
          suite.specs.forEach((spec: any) => {
            if (spec.tests) {
              spec.tests.forEach((test: any) => {
                total++;
                if (test.results && test.results.some((r: any) => r.status === 'passed')) {
                  passed++;
                }
              });
            }
          });
        }
      }
    });
  }
  
  return total > 0 ? Math.round((passed / total) * 100) : 0;
}

// 生成Markdown报告
function generateMarkdownReport(summary: any): string {
  const passRate = summary.totalTests > 0 
    ? Math.round((summary.passedTests / summary.totalTests) * 100) 
    : 0;
  
  return `# E2E测试报告

## 📊 测试概览

- **测试时间**: ${new Date(summary.timestamp).toLocaleString('zh-CN')}
- **总测试数**: ${summary.totalTests}
- **通过率**: ${passRate}% (${summary.passedTests}/${summary.totalTests})
- **执行时长**: ${Math.round(summary.duration / 1000)}秒

## 🎯 测试结果

| 状态 | 数量 | 百分比 |
|------|------|--------|
| ✅ 通过 | ${summary.passedTests} | ${Math.round((summary.passedTests / summary.totalTests) * 100)}% |
| ❌ 失败 | ${summary.failedTests} | ${Math.round((summary.failedTests / summary.totalTests) * 100)}% |
| ⏭️ 跳过 | ${summary.skippedTests} | ${Math.round((summary.skippedTests / summary.totalTests) * 100)}% |

## 🌐 浏览器支持

测试的浏览器: ${summary.browsers.join(', ')}

## 📋 功能覆盖率

| 功能模块 | 覆盖率 |
|----------|--------|
| 扩展加载 | ${summary.coverage.extensionLoading}% |
| Popup交互 | ${summary.coverage.popupInteractions}% |
| NewTab导航 | ${summary.coverage.newtabNavigation}% |
| 用户流程 | ${summary.coverage.userWorkflows}% |

${summary.failedTestDetails.length > 0 ? `
## ❌ 失败的测试

${summary.failedTestDetails.map((test: any) => `
### ${test.title}
- **文件**: ${test.file}
- **错误**: ${test.error}
- **耗时**: ${test.duration}ms
`).join('\n')}
` : '## ✅ 所有测试都通过了！'}

## 📁 相关文件

- [详细HTML报告](./e2e-report/index.html)
- [JSON结果文件](./e2e-results.json)
- [测试截图和视频](./e2e-artifacts/)

---
*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`;
}

// 清理临时文件
async function cleanupTempFiles(): Promise<void> {
  try {
    const tempDirs = [
      path.resolve(__dirname, '../../.temp'),
      path.resolve(__dirname, '../../node_modules/.cache/playwright')
    ];
    
    for (const dir of tempDirs) {
      if (fs.existsSync(dir)) {
        // 只清理特定的临时文件，不删除整个目录
        console.log(`  🗑️ 清理临时目录: ${dir}`);
      }
    }
    
    console.log('  ✅ 临时文件清理完成');
    
  } catch (error) {
    console.error('  ❌ 清理临时文件失败:', error);
  }
}

// 归档测试结果
async function archiveTestResults(): Promise<void> {
  try {
    const testResultsDir = path.resolve(__dirname, '../../test-results');
    
    if (!fs.existsSync(testResultsDir)) {
      console.log('  ⚠️ 测试结果目录不存在，跳过归档');
      return;
    }
    
    // 创建归档目录
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveDir = path.join(testResultsDir, 'archives', `e2e-${timestamp}`);
    
    if (!fs.existsSync(path.dirname(archiveDir))) {
      fs.mkdirSync(path.dirname(archiveDir), { recursive: true });
    }
    
    // 复制重要文件到归档目录
    const filesToArchive = [
      'e2e-results.json',
      'e2e-summary.json',
      'E2E_TEST_REPORT.md'
    ];
    
    fs.mkdirSync(archiveDir, { recursive: true });
    
    for (const file of filesToArchive) {
      const sourcePath = path.join(testResultsDir, file);
      const targetPath = path.join(archiveDir, file);
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
      }
    }
    
    console.log(`  ✅ 测试结果已归档到: ${archiveDir}`);
    
  } catch (error) {
    console.error('  ❌ 归档测试结果失败:', error);
  }
}

export default globalTeardown;
