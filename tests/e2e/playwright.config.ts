/**
 * Playwright配置文件 - 浏览器扩展E2E测试
 */

import { defineConfig, devices } from '@playwright/test';
import path from 'path';

export default defineConfig({
  testDir: './tests/e2e',
  
  // 全局设置
  fullyParallel: false, // 扩展测试需要串行执行
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : 1, // 扩展测试使用单worker
  
  // 报告配置
  reporter: [
    ['html', { outputFolder: 'test-results/e2e-report' }],
    ['json', { outputFile: 'test-results/e2e-results.json' }],
    ['list']
  ],
  
  // 全局配置
  use: {
    // 基础设置
    actionTimeout: 10000,
    navigationTimeout: 30000,
    
    // 截图和视频
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    
    // 浏览器设置
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // 扩展特定设置
    permissions: ['storage', 'tabs', 'notifications'],
  },

  // 项目配置 - 不同浏览器的扩展测试
  projects: [
    {
      name: 'chrome-extension',
      use: {
        ...devices['Desktop Chrome'],
        channel: 'chrome',
        launchOptions: {
          args: [
            '--disable-extensions-except=' + path.resolve(__dirname, '../../.output/chrome-mv3'),
            '--load-extension=' + path.resolve(__dirname, '../../.output/chrome-mv3'),
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox'
          ],
          headless: false // 扩展测试需要有头模式
        }
      },
    },
    
    {
      name: 'firefox-extension',
      use: {
        ...devices['Desktop Firefox'],
        launchOptions: {
          firefoxUserPrefs: {
            'xpinstall.signatures.required': false,
            'extensions.autoDisableScopes': 0,
            'extensions.enabledScopes': 15
          },
          headless: false
        }
      },
    }
  ],

  // 全局设置和清理
  globalSetup: require.resolve('./global-setup.ts'),
  globalTeardown: require.resolve('./global-teardown.ts'),
  
  // 输出目录
  outputDir: 'test-results/e2e-artifacts',
  
  // 超时设置
  timeout: 60000,
  expect: {
    timeout: 10000
  },
  
  // Web服务器配置（如果需要）
  webServer: {
    command: 'npm run build',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000
  }
});

// 扩展测试辅助函数
export const EXTENSION_CONFIG = {
  chrome: {
    extensionPath: path.resolve(__dirname, '../../.output/chrome-mv3'),
    manifestPath: path.resolve(__dirname, '../../.output/chrome-mv3/manifest.json')
  },
  firefox: {
    extensionPath: path.resolve(__dirname, '../../.output/firefox-mv2'),
    manifestPath: path.resolve(__dirname, '../../.output/firefox-mv2/manifest.json')
  }
};

// 测试数据配置
export const TEST_DATA = {
  tools: {
    popup: [
      { id: 'xuid', name: 'XUID切换助手', icon: '🕹️' },
      { id: 'alert-parser', name: '告警解析器', icon: '🚨' },
      { id: 'task-list', name: '任务列表工具', icon: '📋' }
    ],
    newtab: [
      { id: 'api-diff', name: 'API Diff工具', icon: '🔍' },
      { id: 'example-newtab', name: 'NewTab示例', icon: '🚀' }
    ]
  },
  
  testUrls: {
    popup: 'chrome-extension://*/popup.html',
    newtab: 'chrome-extension://*/newtab.html',
    testPage: 'https://example.com'
  },
  
  selectors: {
    extensionIcon: '[data-testid="extension-icon"]',
    toolCard: '.tool-card',
    toolGrid: '.tool-grid',
    modal: '.modal',
    notification: '.notification',
    loadingIndicator: '.loading'
  },
  
  timeouts: {
    extensionLoad: 5000,
    popupOpen: 3000,
    toolExecution: 10000,
    pageNavigation: 15000,
    dataTransfer: 5000
  }
};

// 扩展ID获取辅助函数
export async function getExtensionId(page: any, browser: string): Promise<string> {
  if (browser === 'chrome') {
    // Chrome扩展ID获取
    await page.goto('chrome://extensions/');
    const extensionId = await page.evaluate(() => {
      const extensions = document.querySelectorAll('extensions-item');
      for (const ext of extensions) {
        const name = ext.shadowRoot?.querySelector('#name')?.textContent;
        if (name?.includes('服务运营工具集合')) {
          return ext.getAttribute('id');
        }
      }
      return null;
    });
    return extensionId;
  } else {
    // Firefox扩展ID获取
    await page.goto('about:debugging#/runtime/this-firefox');
    // Firefox扩展ID获取逻辑
    return 'firefox-extension-id';
  }
}

// 等待扩展加载完成
export async function waitForExtensionLoad(page: any, timeout = 10000): Promise<void> {
  await page.waitForFunction(
    () => {
      return window.chrome && window.chrome.runtime && window.chrome.runtime.id;
    },
    { timeout }
  );
}

// 打开扩展popup
export async function openExtensionPopup(page: any, extensionId: string): Promise<any> {
  const popupUrl = `chrome-extension://${extensionId}/popup.html`;
  const popupPage = await page.context().newPage();
  await popupPage.goto(popupUrl);
  await popupPage.waitForLoadState('domcontentloaded');
  return popupPage;
}

// 验证工具列表
export async function verifyToolsList(page: any, expectedTools: any[]): Promise<boolean> {
  const toolCards = await page.locator(TEST_DATA.selectors.toolCard).all();
  
  if (toolCards.length !== expectedTools.length) {
    return false;
  }
  
  for (let i = 0; i < expectedTools.length; i++) {
    const toolName = await toolCards[i].locator('.tool-name').textContent();
    if (toolName !== expectedTools[i].name) {
      return false;
    }
  }
  
  return true;
}

// 模拟工具执行
export async function executeToolAndVerify(
  page: any, 
  toolId: string, 
  expectedBehavior: 'popup' | 'newtab'
): Promise<boolean> {
  const toolCard = page.locator(`[data-tool-id="${toolId}"]`);
  await toolCard.click();
  
  if (expectedBehavior === 'popup') {
    // 验证popup中的执行
    await page.waitForSelector(TEST_DATA.selectors.modal, { timeout: 5000 });
    return await page.locator(TEST_DATA.selectors.modal).isVisible();
  } else {
    // 验证newtab跳转
    const newPagePromise = page.context().waitForEvent('page');
    const newPage = await newPagePromise;
    await newPage.waitForLoadState('domcontentloaded');
    return newPage.url().includes('newtab.html');
  }
}
