{"name": "fwyy-tools-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for browser extension using Playwright", "scripts": {"test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:chrome": "playwright test --project=chrome-extension", "test:e2e:firefox": "playwright test --project=firefox-extension", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:install-deps": "playwright install-deps"}, "devDependencies": {"@playwright/test": "^1.40.0"}, "keywords": ["playwright", "e2e", "browser-extension", "testing", "chrome", "firefox"]}