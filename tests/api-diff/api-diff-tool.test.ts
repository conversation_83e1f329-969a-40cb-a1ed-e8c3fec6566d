/**
 * API Diff Tool Tests
 * API 差异对比工具主类的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ApiDiffTool } from '../../tools/api-diff-tool';

// Mock DOM environment
const mockDocument = {
  createElement: vi.fn().mockReturnValue({
    className: '',
    innerHTML: '',
    style: {},
    addEventListener: vi.fn(),
    appendChild: vi.fn(),
    removeChild: vi.fn(),
    querySelector: vi.fn(),
    querySelectorAll: vi.fn().mockReturnValue([]),
    classList: {
      add: vi.fn(),
      remove: vi.fn(),
      contains: vi.fn()
    }
  }),
  getElementById: vi.fn().mockReturnValue({
    style: { display: 'none' },
    innerHTML: '',
    addEventListener: vi.fn(),
    appendChild: vi.fn(),
    querySelector: vi.fn(),
    querySelectorAll: vi.fn().mockReturnValue([])
  }),
  body: {
    appendChild: vi.fn(),
    removeChild: vi.fn()
  },
  head: {
    appendChild: vi.fn()
  },
  addEventListener: vi.fn(),
  dispatchEvent: vi.fn()
};

const mockWindow = {
  location: {
    pathname: '/newtab.html',
    href: 'chrome-extension://test/newtab.html'
  }
};

describe('ApiDiffTool', () => {
  let apiDiffTool: ApiDiffTool;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup global mocks
    globalThis.document = mockDocument as any;
    globalThis.window = mockWindow as any;
    
    apiDiffTool = new ApiDiffTool();
  });

  describe('Tool Properties', () => {
    it('should have correct tool metadata', () => {
      expect(apiDiffTool.id).toBe('api-diff-tool');
      expect(apiDiffTool.name).toBe('接口 Diff 测试台');
      expect(apiDiffTool.description).toBe('页面版 Postman + 双端接口请求和可视化差异对比工具');
      expect(apiDiffTool.icon).toBe('🔄');
      expect(apiDiffTool.categories).toEqual(['all', 'development', 'api']);
      expect(apiDiffTool.displayMode).toBe('newtab');
      expect(apiDiffTool.requiresFullscreen).toBe(true);
    });

    it('should have correct version', () => {
      expect(apiDiffTool.version).toEqual({
        major: 1,
        minor: 0,
        patch: 0
      });
    });

    it('should have new tab configuration', () => {
      expect(apiDiffTool.newtabData).toEqual({
        toolId: 'api-diff-tool',
        initialConfig: {
          theme: 'auto',
          layout: 'three-column',
          autoSave: true
        },
        permissions: ['storage', 'activeTab', 'clipboardWrite']
      });
    });
  });

  describe('Environment Detection', () => {
    it('should detect new tab environment correctly', () => {
      // Test private method through action call
      expect(() => apiDiffTool.action()).not.toThrow();
    });

    it('should handle non-newtab environment', () => {
      mockWindow.location.pathname = '/popup.html';
      
      // Should throw error for non-newtab environment
      expect(async () => {
        await apiDiffTool.action();
      }).rejects.toThrow('API Diff Tool 必须在新标签页环境中运行');
    });
  });

  describe('Lifecycle Methods', () => {
    it('should initialize in new tab environment', async () => {
      // Mock successful initialization
      mockDocument.getElementById.mockReturnValue({
        style: { display: 'none' },
        innerHTML: '',
        addEventListener: vi.fn(),
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([])
      });

      await expect(apiDiffTool.onNewTabInit()).resolves.not.toThrow();
    });

    it('should handle initialization errors', async () => {
      // Mock initialization failure
      mockDocument.getElementById.mockReturnValue(null);

      await expect(apiDiffTool.onNewTabInit()).rejects.toThrow();
    });

    it('should clean up on destroy', async () => {
      await apiDiffTool.onNewTabInit();
      await expect(apiDiffTool.onNewTabDestroy()).resolves.not.toThrow();
    });
  });

  describe('Component Integration', () => {
    it('should initialize all components', async () => {
      // Mock container elements
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();
      
      // Verify components are initialized
      expect(mockContainer.innerHTML).toBeDefined();
    });

    it('should handle component initialization failure', async () => {
      // Mock component failure
      mockDocument.getElementById.mockImplementation((id) => {
        if (id === 'tool-container') {
          return null; // Simulate missing container
        }
        return {
          style: { display: 'none' },
          innerHTML: '',
          addEventListener: vi.fn()
        };
      });

      await expect(apiDiffTool.onNewTabInit()).rejects.toThrow();
    });
  });

  describe('Event Handling', () => {
    it('should set up event listeners', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);
      mockDocument.addEventListener = vi.fn();

      await apiDiffTool.onNewTabInit();

      expect(mockDocument.addEventListener).toHaveBeenCalled();
    });

    it('should handle request compare events', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();

      // Simulate request compare event
      const requestConfig = {
        method: 'GET' as const,
        oldUrl: 'https://api-old.example.com/test',
        newUrl: 'https://api-new.example.com/test'
      };

      const event = new CustomEvent('request-compare', {
        detail: requestConfig
      });

      // Should not throw when handling the event
      expect(() => {
        mockDocument.dispatchEvent(event);
      }).not.toThrow();
    });
  });

  describe('State Management', () => {
    it('should manage request execution state', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();

      // Test state management
      const initialState = apiDiffTool.getRequestExecutionState();
      expect(initialState.executing).toBe(false);
      expect(initialState.statusText).toBe('就绪');
    });

    it('should update component states', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();

      // Component states should be managed internally
      expect(() => {
        // This would trigger internal state updates
        apiDiffTool.getRequestExecutionState();
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle action execution errors gracefully', async () => {
      // Mock error condition
      mockDocument.getElementById.mockReturnValue(null);

      await expect(apiDiffTool.action()).rejects.toThrow();
    });

    it('should show error notifications', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);
      mockDocument.body.appendChild = vi.fn();

      await apiDiffTool.onNewTabInit();

      // Error notifications should be handled
      expect(mockDocument.body.appendChild).toHaveBeenCalled();
    });
  });

  describe('Configuration Management', () => {
    it('should handle configuration changes', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();

      // Configuration changes should be handled
      const config = {
        method: 'POST' as const,
        oldUrl: 'https://api.example.com/old',
        newUrl: 'https://api.example.com/new'
      };

      expect(() => {
        // This would trigger configuration handling
        apiDiffTool.getRequestExecutionState();
      }).not.toThrow();
    });
  });

  describe('Resource Cleanup', () => {
    it('should clean up all resources on destroy', async () => {
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();
      await apiDiffTool.onNewTabDestroy();

      // Cleanup should complete without errors
      expect(true).toBe(true);
    });

    it('should handle cleanup errors gracefully', async () => {
      // Mock cleanup error
      const mockContainer = {
        innerHTML: '',
        appendChild: vi.fn(),
        querySelector: vi.fn(),
        querySelectorAll: vi.fn().mockReturnValue([]),
        addEventListener: vi.fn()
      };

      mockDocument.getElementById.mockReturnValue(mockContainer);

      await apiDiffTool.onNewTabInit();

      // Even with errors, cleanup should not throw
      await expect(apiDiffTool.onNewTabDestroy()).resolves.not.toThrow();
    });
  });

  describe('Integration with Tool Registry', () => {
    it('should be compatible with tool registry interface', () => {
      // Test that the tool implements the required interface
      expect(typeof apiDiffTool.action).toBe('function');
      expect(typeof apiDiffTool.onNewTabInit).toBe('function');
      expect(typeof apiDiffTool.onNewTabDestroy).toBe('function');
      expect(apiDiffTool.id).toBeDefined();
      expect(apiDiffTool.name).toBeDefined();
      expect(apiDiffTool.version).toBeDefined();
    });

    it('should have correct display mode for new tab', () => {
      expect(apiDiffTool.displayMode).toBe('newtab');
      expect(apiDiffTool.requiresFullscreen).toBe(true);
    });
  });
});
