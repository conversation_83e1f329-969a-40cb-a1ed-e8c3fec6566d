/**
 * <PERSON><PERSON>l Parser Tests
 * Curl 命令解析器的单元测试
 */

import { describe, it, expect } from 'vitest';
import { CurlParser } from '../../tools/api-diff/utils/curl-parser';

describe('CurlParser', () => {
  describe('parse', () => {
    it('should parse basic GET request', () => {
      const curl = `curl 'https://api.example.com/users'`;
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('GET');
      expect(result.config?.oldUrl).toBe('https://api.example.com/users');
      expect(result.config?.newUrl).toBe('https://api.example.com/users');
    });

    it('should parse POST request with data', () => {
      const curl = `curl -X POST 'https://api.example.com/users' \\
        -H 'Content-Type: application/json' \\
        -d '{"name": "<PERSON>", "email": "<EMAIL>"}'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('POST');
      expect(result.config?.headers?.['Content-Type']).toBe('application/json');
      expect(result.config?.body?.type).toBe('json');
      expect(result.config?.body?.content).toBe('{"name": "John", "email": "<EMAIL>"}');
    });

    it('should parse request with multiple headers', () => {
      const curl = `curl 'https://api.example.com/users' \\
        -H 'Authorization: Bearer token123' \\
        -H 'Content-Type: application/json' \\
        -H 'X-Custom-Header: custom-value'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.headers).toEqual({
        'Authorization': 'Bearer token123',
        'Content-Type': 'application/json',
        'X-Custom-Header': 'custom-value'
      });
    });

    it('should parse request with query parameters in URL', () => {
      const curl = `curl 'https://api.example.com/users?page=1&limit=10&search=john'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.queryParams).toEqual({
        'page': '1',
        'limit': '10',
        'search': 'john'
      });
    });

    it('should parse form data request', () => {
      const curl = `curl -X POST 'https://api.example.com/upload' \\
        -F 'file=@document.pdf' \\
        -F 'description=Important document'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('POST');
      expect(result.config?.body?.type).toBe('form-data');
      expect(result.config?.body?.content).toContain('file=@document.pdf');
      expect(result.config?.body?.content).toContain('description=Important document');
    });

    it('should parse URL encoded data', () => {
      const curl = `curl -X POST 'https://api.example.com/login' \\
        --data-urlencode 'username=<EMAIL>' \\
        --data-urlencode 'password=secret123'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.body?.type).toBe('x-www-form-urlencoded');
      expect(result.config?.body?.content).toContain('username=john%40example.com');
      expect(result.config?.body?.content).toContain('password=secret123');
    });

    it('should parse request with basic authentication', () => {
      const curl = `curl -u 'username:password' 'https://api.example.com/protected'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.auth?.type).toBe('basic');
      expect(result.config?.auth?.credentials?.username).toBe('username');
      expect(result.config?.auth?.credentials?.password).toBe('password');
    });

    it('should parse request with bearer token', () => {
      const curl = `curl 'https://api.example.com/users' \\
        -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.auth?.type).toBe('bearer');
      expect(result.config?.auth?.credentials?.token).toBe('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');
    });

    it('should parse request with custom timeout', () => {
      const curl = `curl --max-time 30 'https://api.example.com/slow-endpoint'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.timeout).toBe(30000); // 30 seconds in milliseconds
    });

    it('should handle different quote styles', () => {
      const curl = `curl -X POST "https://api.example.com/users" \\
        -H "Content-Type: application/json" \\
        -d "{\\"name\\": \\"John\\", \\"email\\": \\"<EMAIL>\\"}"`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('POST');
      expect(result.config?.oldUrl).toBe('https://api.example.com/users');
    });

    it('should handle multiline curl commands', () => {
      const curl = `curl -X POST \\
        'https://api.example.com/users' \\
        -H 'Content-Type: application/json' \\
        -H 'Authorization: Bearer token123' \\
        -d '{
          "name": "John Doe",
          "email": "<EMAIL>",
          "age": 30
        }'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('POST');
      expect(result.config?.headers?.['Content-Type']).toBe('application/json');
      expect(result.config?.headers?.['Authorization']).toBe('Bearer token123');
    });

    it('should handle invalid curl commands', () => {
      const invalidCurls = [
        '',
        'not a curl command',
        'curl',
        'curl invalid-url',
        'wget https://example.com'
      ];

      invalidCurls.forEach(curl => {
        const result = CurlParser.parse(curl);
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      });
    });

    it('should extract method from -X flag', () => {
      const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
      
      methods.forEach(method => {
        const curl = `curl -X ${method} 'https://api.example.com/test'`;
        const result = CurlParser.parse(curl);
        
        expect(result.success).toBe(true);
        expect(result.config?.method).toBe(method);
      });
    });

    it('should handle compressed responses', () => {
      const curl = `curl --compressed 'https://api.example.com/users'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.headers?.['Accept-Encoding']).toContain('gzip');
    });

    it('should handle follow redirects', () => {
      const curl = `curl -L 'https://api.example.com/redirect'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      // Note: redirect handling would be implementation specific
    });

    it('should handle insecure SSL', () => {
      const curl = `curl -k 'https://self-signed.example.com/api'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      // Note: SSL verification handling would be implementation specific
    });

    it('should parse complex real-world example', () => {
      const curl = `curl -X POST 'https://api.github.com/repos/owner/repo/issues' \\
        -H 'Accept: application/vnd.github.v3+json' \\
        -H 'Authorization: token ghp_xxxxxxxxxxxxxxxxxxxx' \\
        -H 'Content-Type: application/json' \\
        -d '{
          "title": "Bug report",
          "body": "Something is broken",
          "labels": ["bug", "high-priority"],
          "assignees": ["username"]
        }'`;
      
      const result = CurlParser.parse(curl);

      expect(result.success).toBe(true);
      expect(result.config?.method).toBe('POST');
      expect(result.config?.oldUrl).toBe('https://api.github.com/repos/owner/repo/issues');
      expect(result.config?.headers?.['Accept']).toBe('application/vnd.github.v3+json');
      expect(result.config?.headers?.['Authorization']).toBe('token ghp_xxxxxxxxxxxxxxxxxxxx');
      expect(result.config?.headers?.['Content-Type']).toBe('application/json');
      expect(result.config?.body?.type).toBe('json');
      expect(result.config?.body?.content).toContain('Bug report');
    });
  });

  describe('extractUrl', () => {
    it('should extract URL from various formats', () => {
      const testCases = [
        { input: `curl 'https://api.example.com'`, expected: 'https://api.example.com' },
        { input: `curl "https://api.example.com"`, expected: 'https://api.example.com' },
        { input: `curl https://api.example.com`, expected: 'https://api.example.com' },
        { input: `curl -X POST 'https://api.example.com/users'`, expected: 'https://api.example.com/users' }
      ];

      testCases.forEach(({ input, expected }) => {
        const url = CurlParser.extractUrl(input);
        expect(url).toBe(expected);
      });
    });
  });

  describe('extractHeaders', () => {
    it('should extract headers from curl command', () => {
      const curl = `curl 'https://api.example.com' \\
        -H 'Content-Type: application/json' \\
        -H 'Authorization: Bearer token' \\
        --header 'X-Custom: value'`;
      
      const headers = CurlParser.extractHeaders(curl);
      
      expect(headers).toEqual({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token',
        'X-Custom': 'value'
      });
    });
  });

  describe('extractMethod', () => {
    it('should extract HTTP method', () => {
      expect(CurlParser.extractMethod('curl -X POST https://api.example.com')).toBe('POST');
      expect(CurlParser.extractMethod('curl --request PUT https://api.example.com')).toBe('PUT');
      expect(CurlParser.extractMethod('curl https://api.example.com')).toBe('GET');
    });
  });

  describe('extractData', () => {
    it('should extract request data', () => {
      const testCases = [
        {
          input: `curl -d '{"name": "test"}' https://api.example.com`,
          expected: { type: 'json', content: '{"name": "test"}' }
        },
        {
          input: `curl --data 'name=test&email=<EMAIL>' https://api.example.com`,
          expected: { type: 'x-www-form-urlencoded', content: 'name=test&email=<EMAIL>' }
        },
        {
          input: `curl -F 'file=@test.txt' https://api.example.com`,
          expected: { type: 'form-data', content: 'file=@test.txt' }
        }
      ];

      testCases.forEach(({ input, expected }) => {
        const data = CurlParser.extractData(input);
        expect(data).toEqual(expected);
      });
    });
  });

  describe('normalizeQuotes', () => {
    it('should normalize different quote styles', () => {
      const input = `curl -H "Content-Type: application/json" 'https://api.example.com'`;
      const normalized = CurlParser.normalizeQuotes(input);
      
      expect(normalized).toContain(`'Content-Type: application/json'`);
      expect(normalized).toContain(`'https://api.example.com'`);
    });
  });

  describe('removeLineBreaks', () => {
    it('should remove line breaks and backslashes', () => {
      const input = `curl -X POST \\
        'https://api.example.com' \\
        -H 'Content-Type: application/json'`;
      
      const cleaned = CurlParser.removeLineBreaks(input);
      
      expect(cleaned).toBe(`curl -X POST 'https://api.example.com' -H 'Content-Type: application/json'`);
    });
  });
});
