/**
 * Config Manager Tests
 * 配置管理器的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ConfigManager } from '../../tools/api-diff/components/config-manager';
import type { RequestConfig, DualExecutionResult } from '../../tools/api-diff/types/api-diff-types';

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let mockStorage: any;

  beforeEach(() => {
    // Mock browser storage
    mockStorage = {
      get: vi.fn().mockResolvedValue({}),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    };

    globalThis.browser = {
      storage: {
        local: mockStorage
      }
    } as any;

    configManager = new ConfigManager();
  });

  describe('saveConfig', () => {
    it('should save configuration successfully', async () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api-old.example.com/users',
        newUrl: 'https://api-new.example.com/users',
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      };

      const result = await configManager.saveConfig(config, 'Test Config');

      expect(result.success).toBe(true);
      expect(result.configId).toBeDefined();
      expect(mockStorage.set).toHaveBeenCalled();
    });

    it('should generate unique config IDs', async () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/test',
        newUrl: 'https://api.example.com/v2/test'
      };

      const result1 = await configManager.saveConfig(config, 'Config 1');
      const result2 = await configManager.saveConfig(config, 'Config 2');

      expect(result1.configId).not.toBe(result2.configId);
    });

    it('should handle save errors', async () => {
      mockStorage.set.mockRejectedValue(new Error('Storage error'));

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/test',
        newUrl: 'https://api.example.com/v2/test'
      };

      const result = await configManager.saveConfig(config, 'Test Config');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Storage error');
    });

    it('should validate config before saving', async () => {
      const invalidConfig: RequestConfig = {
        method: 'GET',
        oldUrl: '', // Invalid empty URL
        newUrl: 'https://api.example.com/test'
      };

      const result = await configManager.saveConfig(invalidConfig, 'Invalid Config');

      expect(result.success).toBe(false);
      expect(result.error).toContain('validation');
    });
  });

  describe('loadConfig', () => {
    it('should load existing configuration', async () => {
      const savedConfig = {
        id: 'test-config-id',
        name: 'Test Config',
        config: {
          method: 'POST',
          oldUrl: 'https://api-old.example.com/users',
          newUrl: 'https://api-new.example.com/users'
        },
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': { 'test-config-id': savedConfig }
      });

      const result = await configManager.loadConfig('test-config-id');

      expect(result.success).toBe(true);
      expect(result.config).toEqual(savedConfig.config);
    });

    it('should handle non-existent config', async () => {
      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': {}
      });

      const result = await configManager.loadConfig('non-existent-id');

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should handle storage errors', async () => {
      mockStorage.get.mockRejectedValue(new Error('Storage error'));

      const result = await configManager.loadConfig('test-id');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Storage error');
    });
  });

  describe('deleteConfig', () => {
    it('should delete existing configuration', async () => {
      const configs = {
        'config-1': { id: 'config-1', name: 'Config 1' },
        'config-2': { id: 'config-2', name: 'Config 2' }
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': configs
      });

      const result = await configManager.deleteConfig('config-1');

      expect(result.success).toBe(true);
      expect(mockStorage.set).toHaveBeenCalledWith({
        'api-diff-tool-configs': { 'config-2': configs['config-2'] }
      });
    });

    it('should handle deletion of non-existent config', async () => {
      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': {}
      });

      const result = await configManager.deleteConfig('non-existent-id');

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });
  });

  describe('getRecentConfigs', () => {
    it('should return recent configurations', async () => {
      const now = Date.now();
      const configs = {
        'config-1': {
          id: 'config-1',
          name: 'Config 1',
          updatedAt: now - 1000
        },
        'config-2': {
          id: 'config-2',
          name: 'Config 2',
          updatedAt: now - 2000
        },
        'config-3': {
          id: 'config-3',
          name: 'Config 3',
          updatedAt: now - 500
        }
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': configs
      });

      const result = await configManager.getRecentConfigs(2);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('config-3'); // Most recent
      expect(result[1].id).toBe('config-1'); // Second most recent
    });

    it('should handle empty storage', async () => {
      mockStorage.get.mockResolvedValue({});

      const result = await configManager.getRecentConfigs(5);

      expect(result).toHaveLength(0);
    });
  });

  describe('exportConfig', () => {
    it('should export configuration as JSON', async () => {
      const config = {
        id: 'test-config',
        name: 'Test Config',
        config: {
          method: 'GET',
          oldUrl: 'https://api.example.com/test',
          newUrl: 'https://api.example.com/v2/test'
        }
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': { 'test-config': config }
      });

      const result = await configManager.exportConfig('test-config');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      const exportedData = JSON.parse(result.data!);
      expect(exportedData.name).toBe('Test Config');
      expect(exportedData.config.method).toBe('GET');
    });

    it('should handle export of non-existent config', async () => {
      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': {}
      });

      const result = await configManager.exportConfig('non-existent');

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });
  });

  describe('importConfig', () => {
    it('should import valid configuration', async () => {
      const exportData = JSON.stringify({
        name: 'Imported Config',
        config: {
          method: 'POST',
          oldUrl: 'https://api.example.com/users',
          newUrl: 'https://api.example.com/v2/users'
        },
        exportedAt: Date.now()
      });

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-configs': {}
      });

      const result = await configManager.importConfig(exportData);

      expect(result.success).toBe(true);
      expect(result.configId).toBeDefined();
      expect(mockStorage.set).toHaveBeenCalled();
    });

    it('should handle invalid JSON', async () => {
      const invalidJson = '{ invalid json }';

      const result = await configManager.importConfig(invalidJson);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid JSON');
    });

    it('should validate imported configuration', async () => {
      const invalidConfig = JSON.stringify({
        name: 'Invalid Config',
        config: {
          method: 'INVALID_METHOD',
          oldUrl: 'not-a-url'
        }
      });

      const result = await configManager.importConfig(invalidConfig);

      expect(result.success).toBe(false);
      expect(result.error).toContain('validation');
    });
  });

  describe('saveHistory', () => {
    it('should save execution history', async () => {
      const executionResult: DualExecutionResult = {
        oldResponse: {
          status: 200,
          statusText: 'OK',
          headers: {},
          data: { id: 1, name: 'test' },
          duration: 150
        },
        newResponse: {
          status: 200,
          statusText: 'OK',
          headers: {},
          data: { id: 1, name: 'test' },
          duration: 120
        },
        oldError: null,
        newError: null,
        oldDuration: 150,
        newDuration: 120,
        totalDuration: 270,
        timestamp: Date.now(),
        success: true,
        bothSucceeded: true,
        bothFailed: false
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-history': []
      });

      const result = await configManager.saveHistory('test-config', executionResult);

      expect(result.success).toBe(true);
      expect(mockStorage.set).toHaveBeenCalled();
    });

    it('should limit history size', async () => {
      const existingHistory = Array.from({ length: 55 }, (_, i) => ({
        id: `history-${i}`,
        timestamp: Date.now() - i * 1000
      }));

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-history': existingHistory
      });

      const executionResult: DualExecutionResult = {
        oldResponse: null,
        newResponse: null,
        oldError: null,
        newError: null,
        oldDuration: 0,
        newDuration: 0,
        totalDuration: 0,
        timestamp: Date.now(),
        success: false,
        bothSucceeded: false,
        bothFailed: true
      };

      await configManager.saveHistory('test-config', executionResult);

      const setCall = mockStorage.set.mock.calls[0][0];
      const savedHistory = setCall['api-diff-tool-history'];
      
      expect(savedHistory.length).toBeLessThanOrEqual(50); // Should be limited to 50
    });
  });

  describe('getHistory', () => {
    it('should return execution history', async () => {
      const history = [
        {
          id: 'history-1',
          configId: 'config-1',
          timestamp: Date.now() - 1000,
          success: true
        },
        {
          id: 'history-2',
          configId: 'config-2',
          timestamp: Date.now() - 2000,
          success: false
        }
      ];

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-history': history
      });

      const result = await configManager.getHistory();

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('history-1'); // Most recent first
    });

    it('should filter history by config ID', async () => {
      const history = [
        { id: 'history-1', configId: 'config-1', timestamp: Date.now() },
        { id: 'history-2', configId: 'config-2', timestamp: Date.now() },
        { id: 'history-3', configId: 'config-1', timestamp: Date.now() }
      ];

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-history': history
      });

      const result = await configManager.getHistory('config-1');

      expect(result).toHaveLength(2);
      expect(result.every(h => h.configId === 'config-1')).toBe(true);
    });
  });

  describe('getSettings', () => {
    it('should return default settings when none exist', async () => {
      mockStorage.get.mockResolvedValue({});

      const settings = await configManager.getSettings();

      expect(settings.theme).toBe('auto');
      expect(settings.autoSave).toBe(true);
      expect(settings.defaultTimeout).toBe(10000);
    });

    it('should return saved settings', async () => {
      const savedSettings = {
        theme: 'dark',
        autoSave: false,
        defaultTimeout: 5000,
        defaultIgnoreFields: ['timestamp', 'traceId']
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-settings': savedSettings
      });

      const settings = await configManager.getSettings();

      expect(settings.theme).toBe('dark');
      expect(settings.autoSave).toBe(false);
      expect(settings.defaultTimeout).toBe(5000);
    });
  });

  describe('updateSettings', () => {
    it('should update settings', async () => {
      const currentSettings = {
        theme: 'auto',
        autoSave: true,
        defaultTimeout: 10000
      };

      mockStorage.get.mockResolvedValue({
        'api-diff-tool-settings': currentSettings
      });

      const updates = {
        theme: 'dark' as const,
        defaultTimeout: 15000
      };

      const result = await configManager.updateSettings(updates);

      expect(result.success).toBe(true);
      expect(mockStorage.set).toHaveBeenCalledWith({
        'api-diff-tool-settings': {
          ...currentSettings,
          ...updates
        }
      });
    });

    it('should handle settings update errors', async () => {
      mockStorage.set.mockRejectedValue(new Error('Storage error'));

      const result = await configManager.updateSettings({ theme: 'dark' });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Storage error');
    });
  });

  describe('clearAllData', () => {
    it('should clear all stored data', async () => {
      const result = await configManager.clearAllData();

      expect(result.success).toBe(true);
      expect(mockStorage.remove).toHaveBeenCalledWith([
        'api-diff-tool-configs',
        'api-diff-tool-history',
        'api-diff-tool-settings'
      ]);
    });

    it('should handle clear errors', async () => {
      mockStorage.remove.mockRejectedValue(new Error('Clear error'));

      const result = await configManager.clearAllData();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Clear error');
    });
  });

  describe('destroy', () => {
    it('should clean up resources', () => {
      expect(() => configManager.destroy()).not.toThrow();
    });
  });
});
