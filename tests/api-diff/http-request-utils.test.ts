/**
 * HTTP Request Utils Tests
 * HTTP 请求工具的单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HttpRequestUtils } from '../../tools/api-diff/utils/http-request-utils';
import type { RequestConfig } from '../../tools/api-diff/types/api-diff-types';

describe('HttpRequestUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset fetch mock
    globalThis.fetch = vi.fn();
  });

  describe('buildRequestOptions', () => {
    it('should build basic request options', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        timeout: 5000
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.method).toBe('GET');
      expect(options.headers).toEqual({});
      expect(options.body).toBeUndefined();
    });

    it('should include headers', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token123'
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.headers).toEqual({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
      });
    });

    it('should include JSON body for POST requests', () => {
      const config: RequestConfig = {
        method: 'POST',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        body: {
          type: 'json',
          content: '{"name": "test", "email": "<EMAIL>"}'
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.method).toBe('POST');
      expect(options.body).toBe('{"name": "test", "email": "<EMAIL>"}');
      expect(options.headers['Content-Type']).toBe('application/json');
    });

    it('should include form data body', () => {
      const config: RequestConfig = {
        method: 'POST',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        body: {
          type: 'form-data',
          content: 'name=test&email=<EMAIL>'
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.body).toBe('name=test&email=<EMAIL>');
      expect(options.headers['Content-Type']).toBe('multipart/form-data');
    });

    it('should include URL encoded body', () => {
      const config: RequestConfig = {
        method: 'POST',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        body: {
          type: 'x-www-form-urlencoded',
          content: 'name=test&email=test%40example.com'
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.body).toBe('name=test&email=test%40example.com');
      expect(options.headers['Content-Type']).toBe('application/x-www-form-urlencoded');
    });

    it('should handle basic authentication', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        auth: {
          type: 'basic',
          credentials: {
            username: 'testuser',
            password: 'testpass'
          }
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.headers['Authorization']).toBe('Basic dGVzdHVzZXI6dGVzdHBhc3M=');
    });

    it('should handle bearer authentication', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        auth: {
          type: 'bearer',
          credentials: {
            token: 'abc123token'
          }
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.headers['Authorization']).toBe('Bearer abc123token');
    });

    it('should handle custom authentication', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        auth: {
          type: 'custom',
          credentials: {
            headerName: 'X-API-Key',
            headerValue: 'secret123'
          }
        }
      };

      const options = HttpRequestUtils.buildRequestOptions(config, 'old');
      
      expect(options.headers['X-API-Key']).toBe('secret123');
    });
  });

  describe('buildUrl', () => {
    it('should build URL without query parameters', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users'
      };

      expect(HttpRequestUtils.buildUrl(config, 'old')).toBe('https://api.example.com/users');
      expect(HttpRequestUtils.buildUrl(config, 'new')).toBe('https://api.example.com/v2/users');
    });

    it('should build URL with query parameters', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        queryParams: {
          page: '1',
          limit: '10',
          search: 'test user'
        }
      };

      const oldUrl = HttpRequestUtils.buildUrl(config, 'old');
      const newUrl = HttpRequestUtils.buildUrl(config, 'new');
      
      expect(oldUrl).toBe('https://api.example.com/users?page=1&limit=10&search=test%20user');
      expect(newUrl).toBe('https://api.example.com/v2/users?page=1&limit=10&search=test%20user');
    });

    it('should preserve existing query parameters in URL', () => {
      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users?existing=value',
        newUrl: 'https://api.example.com/v2/users',
        queryParams: {
          page: '1'
        }
      };

      const oldUrl = HttpRequestUtils.buildUrl(config, 'old');
      
      expect(oldUrl).toBe('https://api.example.com/users?existing=value&page=1');
    });
  });

  describe('executeRequest', () => {
    it('should execute successful request', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ id: 1, name: 'test' }),
        text: vi.fn().mockResolvedValue('{"id": 1, "name": "test"}')
      };

      globalThis.fetch = vi.fn().mockResolvedValue(mockResponse);

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users/1',
        newUrl: 'https://api.example.com/v2/users/1',
        timeout: 5000
      };

      const result = await HttpRequestUtils.executeRequest(config, 'old');

      expect(result.success).toBe(true);
      expect(result.response?.status).toBe(200);
      expect(result.response?.data).toEqual({ id: 1, name: 'test' });
      expect(result.error).toBeNull();
      expect(result.duration).toBeGreaterThan(0);
    });

    it('should handle HTTP error responses', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ error: 'User not found' }),
        text: vi.fn().mockResolvedValue('{"error": "User not found"}')
      };

      globalThis.fetch = vi.fn().mockResolvedValue(mockResponse);

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users/999',
        newUrl: 'https://api.example.com/v2/users/999',
        timeout: 5000
      };

      const result = await HttpRequestUtils.executeRequest(config, 'old');

      expect(result.success).toBe(false);
      expect(result.response?.status).toBe(404);
      expect(result.response?.data).toEqual({ error: 'User not found' });
      expect(result.error).toContain('HTTP 404');
    });

    it('should handle network errors', async () => {
      globalThis.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        timeout: 5000
      };

      const result = await HttpRequestUtils.executeRequest(config, 'old');

      expect(result.success).toBe(false);
      expect(result.response).toBeNull();
      expect(result.error).toContain('Network error');
    });

    it('should handle timeout', async () => {
      // Mock a slow response
      globalThis.fetch = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 2000))
      );

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/users',
        newUrl: 'https://api.example.com/v2/users',
        timeout: 100 // Very short timeout
      };

      const result = await HttpRequestUtils.executeRequest(config, 'old');

      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('should parse non-JSON responses', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Map([['content-type', 'text/plain']]),
        json: vi.fn().mockRejectedValue(new Error('Not JSON')),
        text: vi.fn().mockResolvedValue('Plain text response')
      };

      globalThis.fetch = vi.fn().mockResolvedValue(mockResponse);

      const config: RequestConfig = {
        method: 'GET',
        oldUrl: 'https://api.example.com/health',
        newUrl: 'https://api.example.com/v2/health',
        timeout: 5000
      };

      const result = await HttpRequestUtils.executeRequest(config, 'old');

      expect(result.success).toBe(true);
      expect(result.response?.data).toBe('Plain text response');
    });
  });

  describe('formatHeaders', () => {
    it('should format headers object', () => {
      const headers = new Map([
        ['content-type', 'application/json'],
        ['authorization', 'Bearer token123'],
        ['x-custom-header', 'custom-value']
      ]);

      const formatted = HttpRequestUtils.formatHeaders(headers);

      expect(formatted).toEqual({
        'content-type': 'application/json',
        'authorization': 'Bearer token123',
        'x-custom-header': 'custom-value'
      });
    });

    it('should handle empty headers', () => {
      const headers = new Map();
      const formatted = HttpRequestUtils.formatHeaders(headers);
      expect(formatted).toEqual({});
    });
  });

  describe('parseResponseData', () => {
    it('should parse JSON response', async () => {
      const mockResponse = {
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockResolvedValue({ success: true }),
        text: vi.fn().mockResolvedValue('{"success": true}')
      };

      const data = await HttpRequestUtils.parseResponseData(mockResponse as any);
      expect(data).toEqual({ success: true });
    });

    it('should fallback to text for invalid JSON', async () => {
      const mockResponse = {
        headers: new Map([['content-type', 'application/json']]),
        json: vi.fn().mockRejectedValue(new Error('Invalid JSON')),
        text: vi.fn().mockResolvedValue('Invalid JSON content')
      };

      const data = await HttpRequestUtils.parseResponseData(mockResponse as any);
      expect(data).toBe('Invalid JSON content');
    });

    it('should parse text response', async () => {
      const mockResponse = {
        headers: new Map([['content-type', 'text/plain']]),
        json: vi.fn(),
        text: vi.fn().mockResolvedValue('Plain text content')
      };

      const data = await HttpRequestUtils.parseResponseData(mockResponse as any);
      expect(data).toBe('Plain text content');
      expect(mockResponse.json).not.toHaveBeenCalled();
    });
  });
});
