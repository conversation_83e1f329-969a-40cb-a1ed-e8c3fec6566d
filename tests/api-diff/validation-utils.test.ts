/**
 * Validation Utils Tests
 * 验证工具的单元测试
 */

import { describe, it, expect } from 'vitest';
import { ValidationUtils } from '../../tools/api-diff/utils/validation-utils';
import type { RequestConfig } from '../../tools/api-diff/types/api-diff-types';

describe('ValidationUtils', () => {
  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      expect(ValidationUtils.validateUrl('https://api.example.com')).toBeNull();
      expect(ValidationUtils.validateUrl('http://localhost:3000')).toBeNull();
      expect(ValidationUtils.validateUrl('https://api.example.com/v1/users')).toBeNull();
    });

    it('should reject invalid URLs', () => {
      expect(ValidationUtils.validateUrl('')).toBe('URL is required');
      expect(ValidationUtils.validateUrl('   ')).toBe('URL is required');
      expect(ValidationUtils.validateUrl('not-a-url')).toBe('Invalid URL format');
      expect(ValidationUtils.validateUrl('ftp://example.com')).toBe('URL must use HTTP or HTTPS protocol');
    });

    it('should reject URLs without hostname', () => {
      expect(ValidationUtils.validateUrl('https://')).toBe('URL must have a valid hostname');
    });
  });

  describe('validateJson', () => {
    it('should validate correct JSON', () => {
      expect(ValidationUtils.validateJson('{}')).toBeNull();
      expect(ValidationUtils.validateJson('{"name": "test"}')).toBeNull();
      expect(ValidationUtils.validateJson('[]')).toBeNull();
      expect(ValidationUtils.validateJson('null')).toBeNull();
      expect(ValidationUtils.validateJson('true')).toBeNull();
      expect(ValidationUtils.validateJson('123')).toBeNull();
    });

    it('should allow empty JSON', () => {
      expect(ValidationUtils.validateJson('')).toBeNull();
      expect(ValidationUtils.validateJson('   ')).toBeNull();
    });

    it('should reject invalid JSON', () => {
      expect(ValidationUtils.validateJson('{')).toContain('Invalid JSON');
      expect(ValidationUtils.validateJson('{"name": }')).toContain('Invalid JSON');
      expect(ValidationUtils.validateJson('undefined')).toContain('Invalid JSON');
    });

    it('should provide helpful error messages', () => {
      const result = ValidationUtils.validateJson('{"name": "test",}');
      expect(result).toContain('Invalid JSON');
    });
  });

  describe('validateMethodBodyCompatibility', () => {
    it('should allow body for POST, PUT, PATCH', () => {
      expect(ValidationUtils.validateMethodBodyCompatibility('POST', true)).toBeNull();
      expect(ValidationUtils.validateMethodBodyCompatibility('PUT', true)).toBeNull();
      expect(ValidationUtils.validateMethodBodyCompatibility('PATCH', true)).toBeNull();
    });

    it('should reject body for GET, HEAD, OPTIONS', () => {
      expect(ValidationUtils.validateMethodBodyCompatibility('GET', true))
        .toBe('GET requests should not have a request body');
      expect(ValidationUtils.validateMethodBodyCompatibility('HEAD', true))
        .toBe('HEAD requests should not have a request body');
      expect(ValidationUtils.validateMethodBodyCompatibility('OPTIONS', true))
        .toBe('OPTIONS requests should not have a request body');
    });

    it('should allow no body for any method', () => {
      expect(ValidationUtils.validateMethodBodyCompatibility('GET', false)).toBeNull();
      expect(ValidationUtils.validateMethodBodyCompatibility('POST', false)).toBeNull();
    });
  });

  describe('validateHeaders', () => {
    it('should validate correct headers', () => {
      expect(ValidationUtils.validateHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
      })).toBeNull();
    });

    it('should reject empty header names', () => {
      expect(ValidationUtils.validateHeaders({
        '': 'value'
      })).toBe('Header name cannot be empty');
    });

    it('should reject invalid header names', () => {
      expect(ValidationUtils.validateHeaders({
        'Content Type': 'application/json'
      })).toContain('Invalid header name');
      
      expect(ValidationUtils.validateHeaders({
        'Content@Type': 'application/json'
      })).toContain('Invalid header name');
    });

    it('should reject header values with line breaks', () => {
      expect(ValidationUtils.validateHeaders({
        'Content-Type': 'application/json\nmalicious'
      })).toContain('cannot contain line breaks');
    });
  });

  describe('validateQueryParams', () => {
    it('should validate correct query parameters', () => {
      expect(ValidationUtils.validateQueryParams({
        'page': '1',
        'limit': '10',
        'search': 'test query'
      })).toBeNull();
    });

    it('should reject empty parameter names', () => {
      expect(ValidationUtils.validateQueryParams({
        '': 'value'
      })).toBe('Query parameter name cannot be empty');
    });

    it('should reject parameter names with special characters', () => {
      expect(ValidationUtils.validateQueryParams({
        'param=name': 'value'
      })).toContain('contains invalid characters');
      
      expect(ValidationUtils.validateQueryParams({
        'param&name': 'value'
      })).toContain('contains invalid characters');
    });
  });

  describe('validateAuth', () => {
    it('should allow no auth', () => {
      expect(ValidationUtils.validateAuth(undefined)).toBeNull();
    });

    it('should validate basic auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'basic',
        credentials: {
          username: 'user',
          password: 'pass'
        }
      })).toBeNull();
    });

    it('should reject incomplete basic auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'basic',
        credentials: {
          username: 'user'
        }
      })).toBe('Basic authentication requires both username and password');
    });

    it('should validate bearer auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'bearer',
        credentials: {
          token: 'abc123'
        }
      })).toBeNull();
    });

    it('should reject incomplete bearer auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'bearer',
        credentials: {}
      })).toBe('Bearer authentication requires a token');
    });

    it('should validate custom auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'custom',
        credentials: {
          headerName: 'X-API-Key',
          headerValue: 'secret123'
        }
      })).toBeNull();
    });

    it('should reject incomplete custom auth', () => {
      expect(ValidationUtils.validateAuth({
        type: 'custom',
        credentials: {
          headerName: 'X-API-Key'
        }
      })).toBe('Custom authentication requires both header name and value');
    });
  });

  describe('validateTimeout', () => {
    it('should validate reasonable timeouts', () => {
      expect(ValidationUtils.validateTimeout(1000)).toBeNull();
      expect(ValidationUtils.validateTimeout(30000)).toBeNull();
      expect(ValidationUtils.validateTimeout(300000)).toBeNull();
    });

    it('should reject zero or negative timeouts', () => {
      expect(ValidationUtils.validateTimeout(0)).toBe('Timeout must be greater than 0');
      expect(ValidationUtils.validateTimeout(-1000)).toBe('Timeout must be greater than 0');
    });

    it('should reject excessive timeouts', () => {
      expect(ValidationUtils.validateTimeout(400000)).toBe('Timeout cannot exceed 5 minutes (300000ms)');
    });
  });

  describe('validateRequestConfig', () => {
    const validConfig: RequestConfig = {
      method: 'POST',
      oldUrl: 'https://api-old.example.com/users',
      newUrl: 'https://api-new.example.com/users',
      headers: {
        'Content-Type': 'application/json'
      },
      queryParams: {
        'page': '1'
      },
      body: {
        type: 'json',
        content: '{"name": "test"}'
      },
      timeout: 10000,
      ignoreFields: ['timestamp']
    };

    it('should validate complete valid config', () => {
      const result = ValidationUtils.validateRequestConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    it('should collect multiple validation errors', () => {
      const invalidConfig: RequestConfig = {
        ...validConfig,
        oldUrl: '',
        newUrl: 'invalid-url',
        body: {
          type: 'json',
          content: '{"invalid": json}'
        },
        timeout: -1000
      };

      const result = ValidationUtils.validateRequestConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.oldUrl).toBeDefined();
      expect(result.errors.newUrl).toBeDefined();
      expect(result.errors.body).toBeDefined();
      expect(result.errors.timeout).toBeDefined();
    });

    it('should validate method-body compatibility', () => {
      const configWithGetAndBody: RequestConfig = {
        ...validConfig,
        method: 'GET',
        body: {
          type: 'json',
          content: '{"data": "test"}'
        }
      };

      const result = ValidationUtils.validateRequestConfig(configWithGetAndBody);
      expect(result.isValid).toBe(false);
      expect(result.errors.method).toContain('GET requests should not have a request body');
    });
  });

  describe('validateField', () => {
    it('should validate individual fields', () => {
      expect(ValidationUtils.validateField('oldUrl', 'https://example.com')).toBeNull();
      expect(ValidationUtils.validateField('json', '{"valid": true}')).toBeNull();
      expect(ValidationUtils.validateField('timeout', 5000)).toBeNull();
    });

    it('should return errors for invalid fields', () => {
      expect(ValidationUtils.validateField('oldUrl', 'invalid')).toContain('Invalid URL');
      expect(ValidationUtils.validateField('json', '{')).toContain('Invalid JSON');
      expect(ValidationUtils.validateField('timeout', -1)).toContain('greater than 0');
    });

    it('should return null for unknown fields', () => {
      expect(ValidationUtils.validateField('unknownField', 'value')).toBeNull();
    });
  });

  describe('formatErrorMessage', () => {
    it('should format error messages with field labels', () => {
      expect(ValidationUtils.formatErrorMessage('oldUrl', 'Invalid format'))
        .toBe('Old API URL: Invalid format');
      expect(ValidationUtils.formatErrorMessage('headers', 'Missing required header'))
        .toBe('Headers: Missing required header');
    });

    it('should handle unknown fields', () => {
      expect(ValidationUtils.formatErrorMessage('unknownField', 'Some error'))
        .toBe('unknownField: Some error');
    });
  });

  describe('getValidationClass', () => {
    it('should return correct CSS classes', () => {
      expect(ValidationUtils.getValidationClass(true)).toBe('is-valid');
      expect(ValidationUtils.getValidationClass(false)).toBe('is-invalid');
    });
  });
});
