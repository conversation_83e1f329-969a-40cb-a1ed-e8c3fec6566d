/**
 * Diff Utils Tests
 * 差异计算工具的单元测试
 */

import { describe, it, expect } from 'vitest';
import { DiffUtils } from '../../tools/api-diff/utils/diff-utils';
import type { DiffOptions } from '../../tools/api-diff/types/api-diff-types';

describe('DiffUtils', () => {
  describe('calculateDiff', () => {
    it('should detect no differences for identical objects', () => {
      const obj1 = { name: '<PERSON>', age: 30, active: true };
      const obj2 = { name: '<PERSON>', age: 30, active: true };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(false);
      expect(diff.changes).toHaveLength(0);
    });

    it('should detect property value changes', () => {
      const obj1 = { name: '<PERSON>', age: 30, active: true };
      const obj2 = { name: '<PERSON>', age: 31, active: false };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      expect(diff.changes).toHaveLength(2);
      
      const ageChange = diff.changes.find(c => c.path === 'age');
      expect(ageChange?.type).toBe('modified');
      expect(ageChange?.oldValue).toBe(30);
      expect(ageChange?.newValue).toBe(31);
      
      const activeChange = diff.changes.find(c => c.path === 'active');
      expect(activeChange?.type).toBe('modified');
      expect(activeChange?.oldValue).toBe(true);
      expect(activeChange?.newValue).toBe(false);
    });

    it('should detect added properties', () => {
      const obj1 = { name: 'John', age: 30 };
      const obj2 = { name: 'John', age: 30, email: '<EMAIL>', active: true };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      const emailChange = diff.changes.find(c => c.path === 'email');
      expect(emailChange?.type).toBe('added');
      expect(emailChange?.newValue).toBe('<EMAIL>');
      
      const activeChange = diff.changes.find(c => c.path === 'active');
      expect(activeChange?.type).toBe('added');
      expect(activeChange?.newValue).toBe(true);
    });

    it('should detect removed properties', () => {
      const obj1 = { name: 'John', age: 30, email: '<EMAIL>', active: true };
      const obj2 = { name: 'John', age: 30 };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      const emailChange = diff.changes.find(c => c.path === 'email');
      expect(emailChange?.type).toBe('removed');
      expect(emailChange?.oldValue).toBe('<EMAIL>');
      
      const activeChange = diff.changes.find(c => c.path === 'active');
      expect(activeChange?.type).toBe('removed');
      expect(activeChange?.oldValue).toBe(true);
    });

    it('should handle nested objects', () => {
      const obj1 = {
        user: {
          name: 'John',
          profile: {
            age: 30,
            location: 'NYC'
          }
        }
      };
      
      const obj2 = {
        user: {
          name: 'John',
          profile: {
            age: 31,
            location: 'LA',
            bio: 'Software developer'
          }
        }
      };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      const ageChange = diff.changes.find(c => c.path === 'user.profile.age');
      expect(ageChange?.type).toBe('modified');
      expect(ageChange?.oldValue).toBe(30);
      expect(ageChange?.newValue).toBe(31);
      
      const locationChange = diff.changes.find(c => c.path === 'user.profile.location');
      expect(locationChange?.type).toBe('modified');
      expect(locationChange?.oldValue).toBe('NYC');
      expect(locationChange?.newValue).toBe('LA');
      
      const bioChange = diff.changes.find(c => c.path === 'user.profile.bio');
      expect(bioChange?.type).toBe('added');
      expect(bioChange?.newValue).toBe('Software developer');
    });

    it('should handle arrays', () => {
      const obj1 = { tags: ['javascript', 'react', 'node'] };
      const obj2 = { tags: ['javascript', 'vue', 'node', 'typescript'] };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      // Array changes should be detected
      const arrayChange = diff.changes.find(c => c.path === 'tags');
      expect(arrayChange?.type).toBe('modified');
    });

    it('should ignore specified fields', () => {
      const obj1 = { name: 'John', timestamp: '2023-01-01', traceId: 'abc123' };
      const obj2 = { name: 'Jane', timestamp: '2023-01-02', traceId: 'def456' };
      
      const options: DiffOptions = {
        ignoreFields: ['timestamp', 'traceId'],
        arrays: { detectMove: false, includeValueOnMove: false },
        textDiff: { minLength: 60 },
        showOnlyChanges: false
      };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2, options);
      
      expect(diff.hasChanges).toBe(true);
      expect(diff.changes).toHaveLength(1);
      
      const nameChange = diff.changes.find(c => c.path === 'name');
      expect(nameChange?.type).toBe('modified');
      expect(nameChange?.oldValue).toBe('John');
      expect(nameChange?.newValue).toBe('Jane');
    });

    it('should handle null and undefined values', () => {
      const obj1 = { name: 'John', age: null, email: undefined };
      const obj2 = { name: 'John', age: 30, email: '<EMAIL>' };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      const ageChange = diff.changes.find(c => c.path === 'age');
      expect(ageChange?.type).toBe('modified');
      expect(ageChange?.oldValue).toBe(null);
      expect(ageChange?.newValue).toBe(30);
      
      const emailChange = diff.changes.find(c => c.path === 'email');
      expect(emailChange?.type).toBe('added');
      expect(emailChange?.newValue).toBe('<EMAIL>');
    });

    it('should handle different data types', () => {
      const obj1 = { value: '123' };
      const obj2 = { value: 123 };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      
      expect(diff.hasChanges).toBe(true);
      
      const valueChange = diff.changes.find(c => c.path === 'value');
      expect(valueChange?.type).toBe('modified');
      expect(valueChange?.oldValue).toBe('123');
      expect(valueChange?.newValue).toBe(123);
    });
  });

  describe('formatDiffForDisplay', () => {
    it('should format diff for display', () => {
      const obj1 = { name: 'John', age: 30 };
      const obj2 = { name: 'Jane', age: 30, email: '<EMAIL>' };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      const formatted = DiffUtils.formatDiffForDisplay(diff);
      
      expect(formatted).toContain('name');
      expect(formatted).toContain('John');
      expect(formatted).toContain('Jane');
      expect(formatted).toContain('email');
      expect(formatted).toContain('<EMAIL>');
    });

    it('should handle empty diff', () => {
      const obj1 = { name: 'John' };
      const obj2 = { name: 'John' };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      const formatted = DiffUtils.formatDiffForDisplay(diff);
      
      expect(formatted).toContain('No differences found');
    });
  });

  describe('getDiffSummary', () => {
    it('should provide diff summary', () => {
      const obj1 = { name: 'John', age: 30, active: true };
      const obj2 = { name: 'Jane', age: 30, email: '<EMAIL>' };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      const summary = DiffUtils.getDiffSummary(diff);
      
      expect(summary.totalChanges).toBe(3);
      expect(summary.added).toBe(1);
      expect(summary.removed).toBe(1);
      expect(summary.modified).toBe(1);
    });

    it('should handle no changes', () => {
      const obj1 = { name: 'John' };
      const obj2 = { name: 'John' };
      
      const diff = DiffUtils.calculateDiff(obj1, obj2);
      const summary = DiffUtils.getDiffSummary(diff);
      
      expect(summary.totalChanges).toBe(0);
      expect(summary.added).toBe(0);
      expect(summary.removed).toBe(0);
      expect(summary.modified).toBe(0);
    });
  });

  describe('compareArrays', () => {
    it('should detect array element changes', () => {
      const arr1 = ['a', 'b', 'c'];
      const arr2 = ['a', 'x', 'c', 'd'];
      
      const diff = DiffUtils.compareArrays(arr1, arr2);
      
      expect(diff.hasChanges).toBe(true);
      expect(diff.changes.length).toBeGreaterThan(0);
    });

    it('should detect identical arrays', () => {
      const arr1 = ['a', 'b', 'c'];
      const arr2 = ['a', 'b', 'c'];
      
      const diff = DiffUtils.compareArrays(arr1, arr2);
      
      expect(diff.hasChanges).toBe(false);
    });

    it('should handle empty arrays', () => {
      const arr1: string[] = [];
      const arr2: string[] = [];
      
      const diff = DiffUtils.compareArrays(arr1, arr2);
      
      expect(diff.hasChanges).toBe(false);
    });

    it('should detect array length changes', () => {
      const arr1 = ['a', 'b'];
      const arr2 = ['a', 'b', 'c'];
      
      const diff = DiffUtils.compareArrays(arr1, arr2);
      
      expect(diff.hasChanges).toBe(true);
    });
  });

  describe('isIgnoredField', () => {
    it('should identify ignored fields', () => {
      const options: DiffOptions = {
        ignoreFields: ['timestamp', 'traceId', 'requestId'],
        arrays: { detectMove: false, includeValueOnMove: false },
        textDiff: { minLength: 60 },
        showOnlyChanges: false
      };
      
      expect(DiffUtils.isIgnoredField('timestamp', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('traceId', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('name', options)).toBe(false);
    });

    it('should handle nested field paths', () => {
      const options: DiffOptions = {
        ignoreFields: ['user.timestamp', 'response.metadata.traceId'],
        arrays: { detectMove: false, includeValueOnMove: false },
        textDiff: { minLength: 60 },
        showOnlyChanges: false
      };
      
      expect(DiffUtils.isIgnoredField('user.timestamp', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('response.metadata.traceId', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('user.name', options)).toBe(false);
    });

    it('should handle wildcard patterns', () => {
      const options: DiffOptions = {
        ignoreFields: ['*.timestamp', '*.traceId'],
        arrays: { detectMove: false, includeValueOnMove: false },
        textDiff: { minLength: 60 },
        showOnlyChanges: false
      };
      
      expect(DiffUtils.isIgnoredField('user.timestamp', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('response.timestamp', options)).toBe(true);
      expect(DiffUtils.isIgnoredField('user.name', options)).toBe(false);
    });
  });

  describe('getChangeType', () => {
    it('should determine change types correctly', () => {
      expect(DiffUtils.getChangeType('value', undefined)).toBe('removed');
      expect(DiffUtils.getChangeType(undefined, 'value')).toBe('added');
      expect(DiffUtils.getChangeType('old', 'new')).toBe('modified');
      expect(DiffUtils.getChangeType('same', 'same')).toBe('unchanged');
    });

    it('should handle null values', () => {
      expect(DiffUtils.getChangeType(null, 'value')).toBe('modified');
      expect(DiffUtils.getChangeType('value', null)).toBe('modified');
      expect(DiffUtils.getChangeType(null, null)).toBe('unchanged');
    });
  });

  describe('deepEqual', () => {
    it('should compare primitive values', () => {
      expect(DiffUtils.deepEqual('test', 'test')).toBe(true);
      expect(DiffUtils.deepEqual(123, 123)).toBe(true);
      expect(DiffUtils.deepEqual(true, true)).toBe(true);
      expect(DiffUtils.deepEqual('test', 'other')).toBe(false);
    });

    it('should compare objects', () => {
      const obj1 = { name: 'John', age: 30 };
      const obj2 = { name: 'John', age: 30 };
      const obj3 = { name: 'Jane', age: 30 };
      
      expect(DiffUtils.deepEqual(obj1, obj2)).toBe(true);
      expect(DiffUtils.deepEqual(obj1, obj3)).toBe(false);
    });

    it('should compare arrays', () => {
      const arr1 = [1, 2, 3];
      const arr2 = [1, 2, 3];
      const arr3 = [1, 2, 4];
      
      expect(DiffUtils.deepEqual(arr1, arr2)).toBe(true);
      expect(DiffUtils.deepEqual(arr1, arr3)).toBe(false);
    });

    it('should handle nested structures', () => {
      const obj1 = { user: { name: 'John', tags: ['a', 'b'] } };
      const obj2 = { user: { name: 'John', tags: ['a', 'b'] } };
      const obj3 = { user: { name: 'John', tags: ['a', 'c'] } };
      
      expect(DiffUtils.deepEqual(obj1, obj2)).toBe(true);
      expect(DiffUtils.deepEqual(obj1, obj3)).toBe(false);
    });
  });
});
