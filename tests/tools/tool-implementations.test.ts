/**
 * 工具类实现测试
 * 测试各个具体工具的核心功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setupBrowserMock, clearBrowserMock, mockBrowser } from '../web-standalone/browser-api-mock';

// 模拟DOM环境
const mockDocument = {
  createElement: vi.fn().mockImplementation((tagName: string) => {
    const element = {
      tagName: tagName.toUpperCase(),
      innerHTML: '',
      style: {},
      classList: {
        add: vi.fn(),
        remove: vi.fn(),
        contains: vi.fn()
      },
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      appendChild: vi.fn(),
      removeChild: vi.fn(),
      querySelector: vi.fn(),
      querySelectorAll: vi.fn().mockReturnValue([])
    };
    return element;
  }),
  body: {
    appendChild: vi.fn(),
    removeChild: vi.fn()
  },
  getElementById: vi.fn(),
  querySelector: vi.fn(),
  querySelectorAll: vi.fn().mockReturnValue([])
};

// 设置全局document
beforeEach(() => {
  // @ts-ignore
  globalThis.document = mockDocument;
});

// 模拟BaseTool基类
class MockBaseTool {
  id: string = '';
  name: string = '';
  description: string = '';
  icon: string = '';
  categories: string[] = ['all'];
  enabled: boolean = true;
  displayMode: 'popup' | 'newtab' = 'popup';
  
  async showNotification(title: string, message: string): Promise<void> {
    await mockBrowser.notifications.create('', {
      type: 'basic',
      iconUrl: '/icon/48.png',
      title,
      message
    });
  }
  
  async copyToClipboard(text: string): Promise<void> {
    // 模拟剪贴板操作
    console.log('复制到剪贴板:', text);
  }
  
  async getCurrentTab(): Promise<any> {
    const tabs = await mockBrowser.tabs.query({ active: true, currentWindow: true });
    return tabs[0];
  }
  
  async executeScript(func: Function): Promise<any> {
    // 模拟脚本执行
    return func();
  }
  
  isInNewTabEnvironment(): boolean {
    // 模拟检查是否在NewTab环境
    return globalThis.location?.pathname === '/newtab.html';
  }
}

// 模拟XuidTool
class MockXuidTool extends MockBaseTool {
  id = 'xuid';
  name = 'XUID切换助手';
  description = 'XUID身份切换工具，支持快速切换Cookie中的XUID值进行调试';
  icon = '🕹️';
  categories = ['all'];
  
  private modal: any = null;
  
  async action(): Promise<void> {
    try {
      // 创建模态框
      this.modal = this.createXuidModal();
      mockDocument.body.appendChild(this.modal);
      
      // 初始化UI组件
      this.initializeUIComponents();
      
      console.log('XUID工具已启动');
    } catch (error) {
      console.error('XUID切换助手启动失败:', error);
      await this.showNotification('错误', 'XUID切换助手启动失败');
      throw error;
    }
  }
  
  private createXuidModal() {
    const modal = mockDocument.createElement('div');
    modal.innerHTML = `
      <div class="xuid-modal">
        <div class="xuid-header">
          <h3>XUID切换助手</h3>
          <button class="close-btn">×</button>
        </div>
        <div class="xuid-content">
          <input type="text" id="xuid-input" placeholder="输入XUID">
          <button id="switch-btn">切换</button>
        </div>
      </div>
    `;
    return modal;
  }
  
  private initializeUIComponents() {
    // 模拟UI组件初始化
    console.log('初始化XUID UI组件');
  }
}

// 模拟AlertParserTool
class MockAlertParserTool extends MockBaseTool {
  id = 'alert-parser';
  name = '告警解析器';
  description = '解析Falcon监控告警信息，自动生成Grafana日志查询跳转链接';
  icon = '🚨';
  categories = ['all'];
  
  async action(): Promise<void> {
    try {
      const modal = this.createModal();
      mockDocument.body.appendChild(modal);
      
      console.log('告警解析器已启动');
    } catch (error) {
      console.error('告警解析器启动失败:', error);
      await this.showNotification('错误', '告警解析器启动失败');
      throw error;
    }
  }
  
  private createModal() {
    const modal = mockDocument.createElement('div');
    modal.innerHTML = `
      <div class="alert-parser-modal">
        <div class="modal-header">
          <h3>告警解析器</h3>
          <button class="close-btn">×</button>
        </div>
        <div class="modal-content">
          <textarea id="alert-input" placeholder="粘贴告警信息"></textarea>
          <button id="parse-btn">解析告警</button>
          <div id="result-section" style="display: none;"></div>
        </div>
      </div>
    `;
    return modal;
  }
  
  parseAlert(alertText: string) {
    // 模拟告警解析逻辑
    if (!alertText.trim()) {
      throw new Error('告警信息不能为空');
    }
    
    return {
      hostInfo: 'test-host',
      alertTime: new Date().toISOString(),
      message: alertText,
      parsed: true
    };
  }
}

// 模拟ExampleNewTabTool
class MockExampleNewTabTool extends MockBaseTool {
  id = 'example-newtab-tool';
  name = 'New Tab 工具示例';
  description = '一个在新标签页中运行的工具示例，展示全屏功能';
  icon = '🚀';
  categories = ['all', 'development'];
  displayMode = 'newtab' as const;
  requiresFullscreen = true;
  
  newtabData = {
    initialConfig: {
      theme: 'dark',
      layout: 'advanced'
    },
    permissions: ['clipboard', 'storage']
  };
  
  async action(): Promise<void> {
    try {
      console.log('🎬 NewTab工具开始执行');
      
      // 检查是否在newtab环境中
      if (!this.isInNewTabEnvironment()) {
        throw new Error('此工具必须在New Tab环境中运行');
      }
      
      // 创建全屏界面
      await this.createFullscreenInterface();
      
      // 初始化工具功能
      await this.initializeFeatures();
      
      console.log('✅ NewTab工具执行完成');
    } catch (error) {
      console.error('❌ NewTab工具执行失败:', error);
      throw error;
    }
  }
  
  async onNewTabInit(): Promise<void> {
    console.log('🔧 NewTab环境初始化');
    await this.loadFullscreenResources();
    this.setupFullscreenEventListeners();
  }
  
  async onNewTabDestroy(): Promise<void> {
    console.log('🧹 NewTab环境清理');
    await this.cleanupResources();
    this.removeEventListeners();
  }
  
  private async createFullscreenInterface(): Promise<void> {
    const container = mockDocument.createElement('div');
    container.innerHTML = `
      <div class="fullscreen-container">
        <h1>NewTab工具示例</h1>
        <div class="tool-content">
          <p>这是一个全屏工具示例</p>
        </div>
      </div>
    `;
    mockDocument.body.appendChild(container);
  }
  
  private async initializeFeatures(): Promise<void> {
    // 模拟异步初始化
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log('✅ 工具功能初始化完成');
  }
  
  private async loadFullscreenResources(): Promise<void> {
    console.log('📦 加载全屏资源');
  }
  
  private setupFullscreenEventListeners(): void {
    console.log('🎧 设置全屏事件监听器');
  }
  
  private async cleanupResources(): Promise<void> {
    console.log('🗑️ 清理资源');
  }
  
  private removeEventListeners(): void {
    console.log('🔇 移除事件监听器');
  }
}

describe('工具类实现测试', () => {
  beforeEach(() => {
    setupBrowserMock();
    vi.clearAllMocks();
  });

  afterEach(() => {
    clearBrowserMock();
  });

  describe('XuidTool测试', () => {
    let xuidTool: MockXuidTool;
    
    beforeEach(() => {
      xuidTool = new MockXuidTool();
    });

    it('应该正确初始化工具属性', () => {
      expect(xuidTool.id).toBe('xuid');
      expect(xuidTool.name).toBe('XUID切换助手');
      expect(xuidTool.displayMode).toBe('popup');
      expect(xuidTool.categories).toContain('all');
    });

    it('应该正确创建XUID切换界面', async () => {
      await xuidTool.action();
      
      // 验证模态框创建
      expect(mockDocument.body.appendChild).toHaveBeenCalled();
      
      // 验证模态框内容
      const modalCall = mockDocument.body.appendChild.mock.calls[0][0];
      expect(modalCall.innerHTML).toContain('XUID切换助手');
      expect(modalCall.innerHTML).toContain('xuid-input');
      expect(modalCall.innerHTML).toContain('switch-btn');
    });

    it('应该正确处理工具启动失败', async () => {
      // 模拟DOM操作失败
      mockDocument.body.appendChild.mockImplementation(() => {
        throw new Error('DOM操作失败');
      });
      
      await expect(xuidTool.action()).rejects.toThrow('DOM操作失败');
      expect(mockBrowser.notifications.create).toHaveBeenCalledWith('', expect.objectContaining({
        title: '错误',
        message: 'XUID切换助手启动失败'
      }));
    });
  });

  describe('AlertParserTool测试', () => {
    let alertTool: MockAlertParserTool;
    
    beforeEach(() => {
      alertTool = new MockAlertParserTool();
    });

    it('应该正确初始化工具属性', () => {
      expect(alertTool.id).toBe('alert-parser');
      expect(alertTool.name).toBe('告警解析器');
      expect(alertTool.icon).toBe('🚨');
    });

    it('应该正确创建告警解析界面', async () => {
      await alertTool.action();
      
      // 验证模态框创建
      expect(mockDocument.body.appendChild).toHaveBeenCalled();
      
      // 验证模态框内容
      const modalCall = mockDocument.body.appendChild.mock.calls[0][0];
      expect(modalCall.innerHTML).toContain('告警解析器');
      expect(modalCall.innerHTML).toContain('alert-input');
      expect(modalCall.innerHTML).toContain('parse-btn');
    });

    it('应该正确解析告警信息', () => {
      const testAlert = 'Test alert message';
      const result = alertTool.parseAlert(testAlert);
      
      expect(result.parsed).toBe(true);
      expect(result.message).toBe(testAlert);
      expect(result.hostInfo).toBe('test-host');
      expect(result.alertTime).toBeDefined();
    });

    it('应该正确处理空告警信息', () => {
      expect(() => alertTool.parseAlert('')).toThrow('告警信息不能为空');
      expect(() => alertTool.parseAlert('   ')).toThrow('告警信息不能为空');
    });
  });

  describe('ExampleNewTabTool测试', () => {
    let newTabTool: MockExampleNewTabTool;
    
    beforeEach(() => {
      newTabTool = new MockExampleNewTabTool();
    });

    it('应该正确初始化NewTab工具属性', () => {
      expect(newTabTool.id).toBe('example-newtab-tool');
      expect(newTabTool.displayMode).toBe('newtab');
      expect(newTabTool.requiresFullscreen).toBe(true);
      expect(newTabTool.newtabData).toBeDefined();
      expect(newTabTool.newtabData.initialConfig.theme).toBe('dark');
    });

    it('应该在NewTab环境中正确执行', async () => {
      // 模拟NewTab环境
      vi.spyOn(newTabTool, 'isInNewTabEnvironment').mockReturnValue(true);
      
      await newTabTool.action();
      
      // 验证全屏界面创建
      expect(mockDocument.body.appendChild).toHaveBeenCalled();
      
      // 验证界面内容
      const containerCall = mockDocument.body.appendChild.mock.calls[0][0];
      expect(containerCall.innerHTML).toContain('NewTab工具示例');
      expect(containerCall.innerHTML).toContain('fullscreen-container');
    });

    it('应该在非NewTab环境中抛出错误', async () => {
      // 模拟非NewTab环境
      vi.spyOn(newTabTool, 'isInNewTabEnvironment').mockReturnValue(false);
      
      await expect(newTabTool.action()).rejects.toThrow('此工具必须在New Tab环境中运行');
    });

    it('应该正确执行NewTab生命周期钩子', async () => {
      const initSpy = vi.spyOn(console, 'log');
      
      await newTabTool.onNewTabInit();
      
      expect(initSpy).toHaveBeenCalledWith('🔧 NewTab环境初始化');
      expect(initSpy).toHaveBeenCalledWith('📦 加载全屏资源');
      expect(initSpy).toHaveBeenCalledWith('🎧 设置全屏事件监听器');
    });

    it('应该正确执行NewTab清理钩子', async () => {
      const logSpy = vi.spyOn(console, 'log');
      
      await newTabTool.onNewTabDestroy();
      
      expect(logSpy).toHaveBeenCalledWith('🧹 NewTab环境清理');
      expect(logSpy).toHaveBeenCalledWith('🗑️ 清理资源');
      expect(logSpy).toHaveBeenCalledWith('🔇 移除事件监听器');
    });
  });

  describe('工具基类功能测试', () => {
    let baseTool: MockBaseTool;
    
    beforeEach(() => {
      baseTool = new MockBaseTool();
    });

    it('应该正确显示通知', async () => {
      await baseTool.showNotification('测试标题', '测试消息');
      
      expect(mockBrowser.notifications.create).toHaveBeenCalledWith('', {
        type: 'basic',
        iconUrl: '/icon/48.png',
        title: '测试标题',
        message: '测试消息'
      });
    });

    it('应该正确获取当前标签页', async () => {
      const mockTab = { id: 1, url: 'https://example.com' };
      mockBrowser.tabs.query.mockResolvedValue([mockTab]);
      
      const tab = await baseTool.getCurrentTab();
      
      expect(mockBrowser.tabs.query).toHaveBeenCalledWith({ active: true, currentWindow: true });
      expect(tab).toEqual(mockTab);
    });

    it('应该正确执行脚本', async () => {
      const testFunction = vi.fn().mockReturnValue('test result');
      
      const result = await baseTool.executeScript(testFunction);
      
      expect(testFunction).toHaveBeenCalled();
      expect(result).toBe('test result');
    });

    it('应该正确检查NewTab环境', () => {
      // 模拟不同的location
      const originalLocation = globalThis.location;
      
      // 测试NewTab环境
      // @ts-ignore
      globalThis.location = { pathname: '/newtab.html' };
      expect(baseTool.isInNewTabEnvironment()).toBe(true);
      
      // 测试非NewTab环境
      // @ts-ignore
      globalThis.location = { pathname: '/popup.html' };
      expect(baseTool.isInNewTabEnvironment()).toBe(false);
      
      // 恢复原始location
      globalThis.location = originalLocation;
    });
  });
});
