<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewTab模拟器 - Web端测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .newtab-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .newtab-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .header-controls {
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .control-btn:hover {
            background: #3367d6;
            transform: translateY(-1px);
        }
        
        .control-btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            color: #333;
        }
        
        .control-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .newtab-content {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .tool-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 40px;
            max-width: 1000px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .tool-icon {
            font-size: 64px;
            margin-bottom: 20px;
            display: block;
        }
        
        .tool-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #333;
        }
        
        .tool-description {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .tool-interface {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
            text-align: left;
        }
        
        .interface-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }
        
        .textarea-field {
            min-height: 120px;
            resize: vertical;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: #3367d6;
            transform: translateY(-1px);
        }
        
        .action-btn.success {
            background: #34a853;
        }
        
        .action-btn.success:hover {
            background: #2d8f47;
        }
        
        .status-panel {
            background: #e8f0fe;
            border: 1px solid #4285f4;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .status-title {
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        
        .status-content {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #333;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .loading-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="newtab-container">
        <div class="newtab-header">
            <div class="header-title">
                <span id="tool-name">NewTab工具</span> - 全屏模式
            </div>
            <div class="header-controls">
                <button class="control-btn secondary" onclick="showDebugInfo()">调试信息</button>
                <button class="control-btn" onclick="resetTool()">重置工具</button>
                <button class="control-btn" onclick="window.close()">关闭</button>
            </div>
        </div>
        
        <div class="newtab-content">
            <div class="tool-container" id="tool-container">
                <!-- 工具内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 模拟工具数据
        const toolConfigs = {
            'api-diff': {
                name: 'API Diff工具',
                icon: '🔍',
                description: '接口返回结果差异对比工具，支持双端对比和差异分析',
                interface: 'api-diff'
            },
            'example-newtab': {
                name: 'NewTab示例工具',
                icon: '🚀',
                description: '展示NewTab环境下的全屏工具功能',
                interface: 'example'
            }
        };

        // 当前工具
        let currentTool = null;
        let toolStatus = [];

        // 日志函数
        function addStatus(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            toolStatus.push(`[${timestamp}] ${prefix} ${message}`);
            updateStatusPanel();
        }

        // 更新状态面板
        function updateStatusPanel() {
            const statusContent = document.querySelector('.status-content');
            if (statusContent) {
                statusContent.textContent = toolStatus.join('\n');
                statusContent.scrollTop = statusContent.scrollHeight;
            }
        }

        // 初始化工具
        function initializeTool() {
            const toolId = getUrlParameter('tool');
            if (!toolId || !toolConfigs[toolId]) {
                showError('未知的工具ID: ' + toolId);
                return;
            }

            currentTool = toolConfigs[toolId];
            document.getElementById('tool-name').textContent = currentTool.name;
            document.title = `${currentTool.name} - NewTab模拟器`;

            addStatus(`初始化工具: ${currentTool.name}`);
            addStatus('检查NewTab环境...');
            addStatus('加载工具配置...');

            // 渲染工具界面
            renderToolInterface();

            addStatus('工具初始化完成', 'success');
        }

        // 渲染工具界面
        function renderToolInterface() {
            const container = document.getElementById('tool-container');
            
            if (currentTool.interface === 'api-diff') {
                container.innerHTML = createApiDiffInterface();
            } else if (currentTool.interface === 'example') {
                container.innerHTML = createExampleInterface();
            } else {
                container.innerHTML = createDefaultInterface();
            }
        }

        // 创建API Diff工具界面
        function createApiDiffInterface() {
            return `
                <span class="tool-icon">${currentTool.icon}</span>
                <h1 class="tool-title">${currentTool.name}</h1>
                <p class="tool-description">${currentTool.description}</p>
                
                <div class="tool-interface">
                    <div class="interface-section">
                        <h3 class="section-title">接口配置</h3>
                        <div class="input-group">
                            <label class="input-label">主接口URL</label>
                            <input type="text" class="input-field" placeholder="https://api.example.com/v1/endpoint" value="https://api.example.com/v1/test">
                        </div>
                        <div class="input-group">
                            <label class="input-label">对比接口URL</label>
                            <input type="text" class="input-field" placeholder="https://api-test.example.com/v1/endpoint" value="https://api-test.example.com/v1/test">
                        </div>
                        <div class="input-group">
                            <label class="input-label">请求参数 (JSON)</label>
                            <textarea class="input-field textarea-field" placeholder='{"key": "value"}'>{
  "userId": "12345",
  "type": "test"
}</textarea>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-btn" onclick="executeApiDiff()">
                            <span class="loading-indicator hidden" id="loading"></span>
                            执行对比
                        </button>
                        <button class="action-btn success" onclick="saveConfig()">保存配置</button>
                    </div>
                    
                    <div class="status-panel">
                        <div class="status-title">执行状态</div>
                        <div class="status-content"></div>
                    </div>
                </div>
            `;
        }

        // 创建示例工具界面
        function createExampleInterface() {
            return `
                <span class="tool-icon">${currentTool.icon}</span>
                <h1 class="tool-title">${currentTool.name}</h1>
                <p class="tool-description">${currentTool.description}</p>
                
                <div class="tool-interface">
                    <div class="interface-section">
                        <h3 class="section-title">示例功能</h3>
                        <div class="input-group">
                            <label class="input-label">测试输入</label>
                            <input type="text" class="input-field" placeholder="输入测试内容" value="Hello NewTab!">
                        </div>
                        <div class="input-group">
                            <label class="input-label">配置选项</label>
                            <select class="input-field">
                                <option value="option1">选项1</option>
                                <option value="option2">选项2</option>
                                <option value="option3">选项3</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-btn" onclick="executeExample()">执行示例</button>
                        <button class="action-btn success" onclick="testFeatures()">测试功能</button>
                    </div>
                    
                    <div class="status-panel">
                        <div class="status-title">执行日志</div>
                        <div class="status-content"></div>
                    </div>
                </div>
            `;
        }

        // 创建默认界面
        function createDefaultInterface() {
            return `
                <span class="tool-icon">${currentTool.icon}</span>
                <h1 class="tool-title">${currentTool.name}</h1>
                <p class="tool-description">${currentTool.description}</p>
                
                <div class="tool-interface">
                    <div class="status-panel">
                        <div class="status-title">工具状态</div>
                        <div class="status-content"></div>
                    </div>
                </div>
            `;
        }

        // 执行API Diff
        function executeApiDiff() {
            const loadingEl = document.getElementById('loading');
            loadingEl.classList.remove('hidden');
            
            addStatus('开始执行API对比...');
            addStatus('发送主接口请求...');
            
            setTimeout(() => {
                addStatus('主接口响应: 200 OK');
                addStatus('发送对比接口请求...');
                
                setTimeout(() => {
                    addStatus('对比接口响应: 200 OK');
                    addStatus('分析响应差异...');
                    
                    setTimeout(() => {
                        addStatus('发现 3 处差异');
                        addStatus('生成差异报告...');
                        addStatus('API对比执行完成', 'success');
                        loadingEl.classList.add('hidden');
                    }, 1000);
                }, 1500);
            }, 1000);
        }

        // 执行示例
        function executeExample() {
            addStatus('执行示例功能...');
            addStatus('初始化示例组件...');
            
            setTimeout(() => {
                addStatus('组件加载完成');
                addStatus('执行示例逻辑...');
                
                setTimeout(() => {
                    addStatus('示例执行完成', 'success');
                }, 1000);
            }, 800);
        }

        // 测试功能
        function testFeatures() {
            addStatus('开始功能测试...');
            const features = ['剪贴板访问', '本地存储', '全屏模式', '键盘快捷键'];
            
            features.forEach((feature, index) => {
                setTimeout(() => {
                    addStatus(`测试 ${feature}...`);
                    setTimeout(() => {
                        addStatus(`${feature} 测试通过`, 'success');
                    }, 500);
                }, index * 1000);
            });
        }

        // 保存配置
        function saveConfig() {
            addStatus('保存配置...');
            setTimeout(() => {
                addStatus('配置保存成功', 'success');
            }, 500);
        }

        // 重置工具
        function resetTool() {
            toolStatus = [];
            addStatus('工具已重置');
            renderToolInterface();
        }

        // 显示调试信息
        function showDebugInfo() {
            const debugInfo = {
                tool: currentTool?.name || 'Unknown',
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                viewport: `${window.innerWidth}x${window.innerHeight}`
            };
            
            alert('调试信息:\n' + JSON.stringify(debugInfo, null, 2));
        }

        // 显示错误
        function showError(message) {
            document.getElementById('tool-container').innerHTML = `
                <div style="text-align: center; color: #ea4335;">
                    <h2>❌ 错误</h2>
                    <p>${message}</p>
                    <button class="action-btn" onclick="window.close()">关闭</button>
                </div>
            `;
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initializeTool();
        });

        // 键盘快捷键
        window.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        resetTool();
                        break;
                    case 'w':
                        e.preventDefault();
                        window.close();
                        break;
                }
            }
        });
    </script>
</body>
</html>
