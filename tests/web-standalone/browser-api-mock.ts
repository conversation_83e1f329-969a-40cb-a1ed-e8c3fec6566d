/**
 * 浏览器扩展API模拟器
 * 用于Web端测试环境，模拟Chrome扩展API
 */

import { vi } from 'vitest';

// 模拟Storage API
const mockStorage = {
  local: {
    get: vi.fn().mockImplementation((keys) => {
      const mockData: Record<string, any> = {
        'newtab-tool-launch': null,
        'tool-settings': {},
        'user-preferences': {}
      };
      
      if (typeof keys === 'string') {
        return Promise.resolve({ [keys]: mockData[keys] });
      } else if (Array.isArray(keys)) {
        const result: Record<string, any> = {};
        keys.forEach(key => {
          result[key] = mockData[key];
        });
        return Promise.resolve(result);
      }
      return Promise.resolve(mockData);
    }),
    
    set: vi.fn().mockImplementation((items) => {
      console.log('Mock Storage Set:', items);
      return Promise.resolve();
    }),
    
    remove: vi.fn().mockImplementation((keys) => {
      console.log('Mock Storage Remove:', keys);
      return Promise.resolve();
    }),
    
    clear: vi.fn().mockResolvedValue(undefined)
  },
  
  sync: {
    get: vi.fn().mockResolvedValue({}),
    set: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined)
  }
};

// 模拟Tabs API
const mockTabs = {
  create: vi.fn().mockImplementation((createProperties) => {
    const mockTab = {
      id: Math.floor(Math.random() * 1000),
      url: createProperties.url,
      active: createProperties.active || false,
      windowId: 1,
      index: 0
    };
    console.log('Mock Tab Created:', mockTab);
    return Promise.resolve(mockTab);
  }),
  
  query: vi.fn().mockImplementation((queryInfo) => {
    const mockTabs = [
      {
        id: 1,
        url: 'https://example.com',
        active: true,
        windowId: 1,
        index: 0
      }
    ];
    return Promise.resolve(mockTabs);
  }),
  
  update: vi.fn().mockResolvedValue({}),
  remove: vi.fn().mockResolvedValue(undefined)
};

// 模拟Runtime API
const mockRuntime = {
  getURL: vi.fn().mockImplementation((path) => {
    return `chrome-extension://mock-extension-id/${path}`;
  }),
  
  sendMessage: vi.fn().mockResolvedValue({}),
  onMessage: {
    addListener: vi.fn(),
    removeListener: vi.fn()
  },
  
  getManifest: vi.fn().mockReturnValue({
    name: '服务运营工具集合',
    version: '1.0.0'
  })
};

// 模拟Notifications API
const mockNotifications = {
  create: vi.fn().mockImplementation((notificationId, options) => {
    console.log('Mock Notification:', notificationId, options);
    return Promise.resolve(notificationId || 'mock-notification-id');
  }),
  
  clear: vi.fn().mockResolvedValue(true),
  getAll: vi.fn().mockResolvedValue({})
};

// 模拟Cookies API
const mockCookies = {
  get: vi.fn().mockResolvedValue(null),
  set: vi.fn().mockResolvedValue({}),
  remove: vi.fn().mockResolvedValue({})
};

// 完整的Browser API Mock
export const mockBrowser = {
  storage: mockStorage,
  tabs: mockTabs,
  runtime: mockRuntime,
  notifications: mockNotifications,
  cookies: mockCookies,
  
  // 权限API
  permissions: {
    contains: vi.fn().mockResolvedValue(true),
    request: vi.fn().mockResolvedValue(true)
  }
};

// 设置全局browser对象
export function setupBrowserMock() {
  // @ts-ignore
  globalThis.browser = mockBrowser;
  // @ts-ignore
  globalThis.chrome = mockBrowser;
  
  console.log('✅ Browser API Mock已设置');
}

// 清理Mock状态
export function clearBrowserMock() {
  vi.clearAllMocks();
  console.log('🧹 Browser API Mock已清理');
}

// 重置特定API的Mock
export function resetStorageMock() {
  mockStorage.local.get.mockClear();
  mockStorage.local.set.mockClear();
  mockStorage.local.remove.mockClear();
}

export function resetTabsMock() {
  mockTabs.create.mockClear();
  mockTabs.query.mockClear();
}

// 设置特定的Mock行为
export function mockStorageFailure() {
  mockStorage.local.set.mockRejectedValue(new Error('Storage quota exceeded'));
}

export function mockTabsFailure() {
  mockTabs.create.mockRejectedValue(new Error('Cannot create tab'));
}

// 验证Mock调用的辅助函数
export function verifyStorageCall(method: 'get' | 'set' | 'remove', expectedArgs?: any) {
  const mockMethod = mockStorage.local[method];
  expect(mockMethod).toHaveBeenCalled();
  
  if (expectedArgs) {
    expect(mockMethod).toHaveBeenCalledWith(expectedArgs);
  }
}

export function verifyTabsCall(method: 'create' | 'query', expectedArgs?: any) {
  const mockMethod = mockTabs[method];
  expect(mockMethod).toHaveBeenCalled();
  
  if (expectedArgs) {
    expect(mockMethod).toHaveBeenCalledWith(expectedArgs);
  }
}
