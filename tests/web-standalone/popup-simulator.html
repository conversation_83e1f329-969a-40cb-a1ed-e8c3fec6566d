<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup模拟器 - Web端测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .simulator-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .simulator-header {
            background: #4285f4;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }
        
        .popup-frame {
            width: 400px;
            height: 600px;
            border: 1px solid #ddd;
            margin: 20px auto;
            background: white;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        
        .popup-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            font-weight: 500;
            color: #495057;
        }
        
        .popup-content {
            padding: 20px;
            height: calc(100% - 60px);
            overflow-y: auto;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .tool-card {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }
        
        .tool-card:hover {
            border-color: #4285f4;
            box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15);
        }
        
        .tool-card.newtab {
            border-left: 4px solid #34a853;
        }
        
        .tool-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .tool-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
            color: #202124;
        }
        
        .tool-description {
            font-size: 12px;
            color: #5f6368;
            line-height: 1.4;
        }
        
        .tool-badge {
            display: inline-block;
            background: #34a853;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-top: 5px;
        }
        
        .test-controls {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #495057;
        }
        
        .control-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        
        .control-button:hover {
            background: #3367d6;
        }
        
        .control-button.secondary {
            background: #6c757d;
        }
        
        .control-button.secondary:hover {
            background: #5a6268;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ready { background: #34a853; }
        .status-testing { background: #fbbc04; }
        .status-error { background: #ea4335; }
    </style>
</head>
<body>
    <div class="simulator-container">
        <div class="simulator-header">
            <span class="status-indicator status-ready"></span>
            Popup模拟器 - Web端功能测试
        </div>
        
        <div class="popup-frame">
            <div class="popup-header">
                服务运营工具集合
            </div>
            <div class="popup-content">
                <div class="tool-grid" id="tool-grid">
                    <!-- 工具卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="test-controls">
            <div class="control-group">
                <label class="control-label">测试控制</label>
                <button class="control-button" onclick="initializeTools()">初始化工具</button>
                <button class="control-button" onclick="testPopupTools()">测试Popup工具</button>
                <button class="control-button" onclick="testNewtabTools()">测试NewTab工具</button>
                <button class="control-button secondary" onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="control-group">
                <label class="control-label">测试日志</label>
                <div class="test-log" id="test-log">等待测试开始...</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟工具数据
        const mockTools = [
            {
                id: 'xuid',
                name: 'XUID切换助手',
                description: 'XUID身份切换工具',
                icon: '🕹️',
                displayMode: 'popup'
            },
            {
                id: 'alert-parser',
                name: '告警解析器',
                description: '解析监控告警信息',
                icon: '🚨',
                displayMode: 'popup'
            },
            {
                id: 'task-list',
                name: '任务列表工具',
                description: '课程任务管理',
                icon: '📋',
                displayMode: 'popup'
            },
            {
                id: 'api-diff',
                name: 'API Diff工具',
                description: '接口返回结果差异对比',
                icon: '🔍',
                displayMode: 'newtab'
            },
            {
                id: 'example-newtab',
                name: 'NewTab示例',
                description: '新标签页工具示例',
                icon: '🚀',
                displayMode: 'newtab'
            }
        ];

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 初始化工具
        function initializeTools() {
            log('开始初始化工具...');
            const toolGrid = document.getElementById('tool-grid');
            
            toolGrid.innerHTML = mockTools.map(tool => `
                <div class="tool-card ${tool.displayMode}" onclick="executeTool('${tool.id}')">
                    <span class="tool-icon">${tool.icon}</span>
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-description">${tool.description}</div>
                    ${tool.displayMode === 'newtab' ? '<span class="tool-badge">NewTab</span>' : ''}
                </div>
            `).join('');
            
            log(`成功初始化 ${mockTools.length} 个工具`, 'success');
        }

        // 执行工具
        function executeTool(toolId) {
            const tool = mockTools.find(t => t.id === toolId);
            if (!tool) {
                log(`工具 ${toolId} 不存在`, 'error');
                return;
            }

            log(`执行工具: ${tool.name} (${tool.displayMode}模式)`);

            if (tool.displayMode === 'popup') {
                // 模拟popup执行
                log(`在popup中执行 ${tool.name}`);
                setTimeout(() => {
                    log(`${tool.name} 执行完成`, 'success');
                }, 1000);
            } else if (tool.displayMode === 'newtab') {
                // 模拟newtab跳转
                log(`准备跳转到新标签页执行 ${tool.name}`);
                log('保存启动数据到Storage...');
                log('创建新标签页...');
                
                setTimeout(() => {
                    log(`${tool.name} 已在新标签页中打开`, 'success');
                    // 模拟打开新窗口
                    window.open(`newtab-simulator.html?tool=${toolId}`, '_blank', 'width=1200,height=800');
                }, 1500);
            }
        }

        // 测试Popup工具
        function testPopupTools() {
            log('开始测试Popup工具...');
            const popupTools = mockTools.filter(t => t.displayMode === 'popup');
            
            popupTools.forEach((tool, index) => {
                setTimeout(() => {
                    executeTool(tool.id);
                }, index * 2000);
            });
        }

        // 测试NewTab工具
        function testNewtabTools() {
            log('开始测试NewTab工具...');
            const newtabTools = mockTools.filter(t => t.displayMode === 'newtab');
            
            newtabTools.forEach((tool, index) => {
                setTimeout(() => {
                    executeTool(tool.id);
                }, index * 3000);
            });
        }

        // 清空日志
        function clearLog() {
            document.getElementById('test-log').textContent = '';
            log('日志已清空');
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            log('Popup模拟器已加载');
            initializeTools();
        });
    </script>
</body>
</html>
