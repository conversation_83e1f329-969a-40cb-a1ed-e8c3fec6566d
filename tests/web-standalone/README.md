# Web端独立测试环境

这个目录包含了用于Web端功能验证的独立测试环境，允许在不依赖浏览器扩展环境的情况下测试核心功能。

## 📁 文件结构

```
tests/web-standalone/
├── README.md                    # 本文档
├── browser-api-mock.ts          # 浏览器API模拟器
├── popup-simulator.html         # Popup界面模拟器
├── newtab-simulator.html        # NewTab界面模拟器
└── web-test-runner.js           # Web测试运行器（待创建）
```

## 🎯 测试目标

### 第一阶段：核心功能验证
- ✅ 工具注册和管理逻辑
- ✅ 页面跳转逻辑（popup → newtab）
- ✅ 数据传递机制
- ✅ 错误处理流程

### 第二阶段：UI交互测试
- ✅ 工具卡片渲染
- ✅ 用户交互响应
- ✅ 模态框创建和管理
- ✅ 表单验证

### 第三阶段：业务逻辑测试
- ✅ 各工具的核心功能
- ✅ 数据处理和转换
- ✅ API调用模拟

## 🚀 使用方法

### 1. 运行单元测试

```bash
# 运行所有Web端测试
npm run test:run

# 运行特定测试文件
npx vitest run tests/core/tool-manager.test.ts
npx vitest run tests/core/data-transfer.test.ts
npx vitest run tests/tools/tool-implementations.test.ts

# 监视模式运行测试
npm run test:watch
```

### 2. 使用可视化模拟器

#### Popup模拟器
```bash
# 在浏览器中打开
open tests/web-standalone/popup-simulator.html
```

**功能特性：**
- 🎨 完整的Popup界面模拟
- 🔧 工具卡片交互测试
- 📊 实时测试日志
- 🎯 分类测试（Popup工具 vs NewTab工具）

**测试步骤：**
1. 点击"初始化工具"按钮
2. 点击不同的工具卡片测试交互
3. 使用"测试Popup工具"进行批量测试
4. 使用"测试NewTab工具"验证跳转逻辑

#### NewTab模拟器
```bash
# 通过Popup模拟器自动打开，或直接访问
open tests/web-standalone/newtab-simulator.html?tool=api-diff
```

**功能特性：**
- 🖥️ 全屏NewTab环境模拟
- 🛠️ 工具特定界面渲染
- 📈 实时状态监控
- ⌨️ 键盘快捷键支持

**支持的工具：**
- `api-diff`: API差异对比工具
- `example-newtab`: NewTab示例工具

### 3. 测试覆盖率检查

```bash
# 生成覆盖率报告
npm run test:coverage

# 查看HTML覆盖率报告
open coverage/index.html
```

## 📋 测试清单

### ✅ 核心功能测试

#### ToolManager测试
- [x] popup模式工具执行
- [x] newtab模式工具跳转
- [x] 工具不存在错误处理
- [x] Storage API失败回退
- [x] Tabs API失败回退

#### 数据传递测试
- [x] NewTabLaunchData构造
- [x] Storage保存和读取
- [x] 数据验证和完整性
- [x] 大数据量处理
- [x] 特殊字符处理

#### 工具实现测试
- [x] XuidTool界面创建
- [x] AlertParserTool解析逻辑
- [x] ExampleNewTabTool生命周期
- [x] 基类功能验证

### ✅ 浏览器API Mock测试

#### Storage API
- [x] get/set/remove操作
- [x] 错误情况模拟
- [x] 数据序列化/反序列化

#### Tabs API
- [x] 标签页创建
- [x] 查询操作
- [x] 错误处理

#### Notifications API
- [x] 通知创建
- [x] 通知清理

## 🔧 配置说明

### Vitest配置
```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      thresholds: {
        global: {
          branches: 85,
          functions: 95,
          lines: 90,
          statements: 90
        }
      }
    }
  }
});
```

### 浏览器API Mock
```typescript
// tests/web-standalone/browser-api-mock.ts
export const mockBrowser = {
  storage: { /* Storage API Mock */ },
  tabs: { /* Tabs API Mock */ },
  runtime: { /* Runtime API Mock */ },
  notifications: { /* Notifications API Mock */ }
};
```

## 📊 测试结果示例

### 成功的测试输出
```
✓ tests/core/tool-manager.test.ts (8)
  ✓ ToolManager页面跳转逻辑测试 (8)
    ✓ executeTool方法 - popup模式 (2)
    ✓ executeTool方法 - newtab模式 (3)
    ✓ executeTool方法 - 错误处理 (3)

✓ tests/core/data-transfer.test.ts (12)
  ✓ NewTab数据传递机制测试 (12)

✓ tests/tools/tool-implementations.test.ts (15)
  ✓ 工具类实现测试 (15)

Test Files  3 passed (3)
Tests       35 passed (35)
Coverage    92.5% (Statements: 185/200)
```

### 覆盖率报告
```
File                    | % Stmts | % Branch | % Funcs | % Lines
------------------------|---------|----------|---------|--------
All files              |   92.5  |   87.3   |   96.2  |   91.8
 core/                 |   94.1  |   89.5   |   97.8  |   93.2
  tool-manager.ts      |   95.2  |   91.2   |   98.5  |   94.8
  data-transfer.ts     |   93.1  |   87.8   |   97.1  |   91.6
 tools/                |   90.8  |   85.1   |   94.6  |   90.4
  tool-implementations |   90.8  |   85.1   |   94.6  |   90.4
```

## 🐛 常见问题

### Q: 测试运行失败，提示"browser is not defined"
**A:** 确保已正确设置browser API mock：
```bash
# 检查setup.ts是否正确导入
cat tests/setup.ts | grep "setupBrowserMock"
```

### Q: 模拟器页面无法正常显示
**A:** 检查浏览器控制台错误，确保JavaScript正常执行：
```javascript
// 在浏览器控制台中检查
console.log(typeof mockTools); // 应该输出 "object"
```

### Q: 覆盖率不达标
**A:** 检查未覆盖的代码分支：
```bash
# 查看详细覆盖率报告
npm run test:coverage
open coverage/index.html
```

## 🔄 下一步

完成Web端测试后，可以进入第二阶段的插件环境集成测试：

1. **构建扩展包**: `npm run build`
2. **加载到浏览器**: 开发者模式加载扩展
3. **运行集成测试**: `npm run test:integration`
4. **执行E2E测试**: `npm run test:e2e`

## 📚 相关文档

- [主测试计划](../../README.md#测试计划)
- [Vitest文档](https://vitest.dev/)
- [jsdom文档](https://github.com/jsdom/jsdom)
- [浏览器扩展API文档](https://developer.chrome.com/docs/extensions/)
