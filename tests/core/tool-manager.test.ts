/**
 * ToolManager核心功能测试
 * 测试页面跳转逻辑和工具执行流程
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setupBrowserMock, clearBrowserMock, mockBrowser, verifyStorageCall, verifyTabsCall } from '../web-standalone/browser-api-mock';

// 模拟工具类
class MockTool {
  constructor(
    public id: string,
    public name: string,
    public displayMode: 'popup' | 'newtab' = 'popup',
    public newtabData?: any
  ) {}
  
  action = vi.fn().mockResolvedValue(undefined);
}

// 模拟ToolRegistry
const mockToolRegistry = {
  getById: vi.fn(),
  getEnabled: vi.fn().mockReturnValue([]),
  register: vi.fn(),
  updateTool: vi.fn()
};

// 模拟NotificationManager
const mockNotificationManager = {
  info: vi.fn(),
  error: vi.fn(),
  success: vi.fn()
};

// 简化的ToolManager类用于测试
class TestToolManager {
  constructor() {}
  
  async executeTool(tool: MockTool) {
    try {
      console.log(`🔍 获取工具实例: ${tool.id}`);
      const toolInstance = mockToolRegistry.getById(tool.id);

      if (!toolInstance) {
        throw new Error(`工具 ${tool.name} 未找到`);
      }

      // 检查是否需要跳转到newtab
      if (toolInstance.displayMode === 'newtab') {
        console.log(`🚀 工具 ${tool.name} 需要在New Tab中执行，开始跳转...`);
        await this.openToolInNewTab(toolInstance);
        return;
      }

      // 原有popup执行逻辑
      if (typeof toolInstance.action === 'function') {
        console.log(`🎬 在popup中执行工具: ${toolInstance.name}`);
        await toolInstance.action();
        console.log(`✅ 工具 ${tool.name} 执行完成`);
      } else {
        throw new Error(`工具 ${tool.name} 的 action 方法不存在`);
      }

    } catch (error) {
      console.error(`❌ 执行工具 ${tool.name} 时出错:`, error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      mockNotificationManager.error(`执行工具失败: ${errorMessage}`);
      throw error;
    }
  }

  private async openToolInNewTab(tool: MockTool) {
    try {
      // 1. 准备传递数据
      const launchData = {
        toolId: tool.id,
        timestamp: Date.now(),
        data: tool.newtabData || {},
        source: 'popup'
      };

      console.log('📦 准备New Tab启动数据:', launchData);

      // 2. 存储到Chrome Storage
      await mockBrowser.storage.local.set({
        'newtab-tool-launch': launchData
      });

      // 3. 显示跳转提示
      mockNotificationManager.info(`正在新标签页中打开 ${tool.name}...`);

      // 4. 打开newtab页面
      const newTabUrl = mockBrowser.runtime.getURL('newtab.html');
      console.log('🌐 打开New Tab页面:', newTabUrl);

      await mockBrowser.tabs.create({
        url: newTabUrl,
        active: true
      });

      // 5. 模拟关闭popup（在测试中不实际关闭）
      console.log('🔄 模拟关闭popup');

    } catch (error) {
      console.error('❌ 打开New Tab失败:', error);
      mockNotificationManager.error('打开新标签页失败，请重试');

      // 回退到popup执行
      if (typeof tool.action === 'function') {
        console.log('🔄 回退到popup执行...');
        await tool.action();
      }
    }
  }
}

describe('ToolManager页面跳转逻辑测试', () => {
  let toolManager: TestToolManager;
  
  beforeEach(() => {
    // 设置浏览器API Mock
    setupBrowserMock();
    
    // 创建ToolManager实例
    toolManager = new TestToolManager();
    
    // 清理所有Mock
    vi.clearAllMocks();
  });

  afterEach(() => {
    clearBrowserMock();
  });

  describe('executeTool方法 - popup模式', () => {
    it('应该在popup模式下直接执行工具', async () => {
      // 准备测试数据
      const popupTool = new MockTool('xuid', 'XUID切换助手', 'popup');
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(popupTool);
      
      // 执行测试
      await toolManager.executeTool(popupTool);
      
      // 验证结果
      expect(mockToolRegistry.getById).toHaveBeenCalledWith('xuid');
      expect(popupTool.action).toHaveBeenCalled();
      expect(mockBrowser.tabs.create).not.toHaveBeenCalled();
      expect(mockBrowser.storage.local.set).not.toHaveBeenCalled();
    });

    it('应该正确处理popup工具执行失败', async () => {
      // 准备测试数据
      const popupTool = new MockTool('xuid', 'XUID切换助手', 'popup');
      popupTool.action.mockRejectedValue(new Error('工具执行失败'));
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(popupTool);
      
      // 执行测试并验证异常
      await expect(toolManager.executeTool(popupTool)).rejects.toThrow('工具执行失败');
      
      // 验证错误处理
      expect(mockNotificationManager.error).toHaveBeenCalledWith('执行工具失败: 工具执行失败');
    });
  });

  describe('executeTool方法 - newtab模式', () => {
    it('应该在newtab模式下跳转到新标签页', async () => {
      // 准备测试数据
      const newtabTool = new MockTool('api-diff', 'API Diff工具', 'newtab', { config: 'test' });
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(newtabTool);
      
      // 执行测试
      await toolManager.executeTool(newtabTool);
      
      // 验证Storage调用
      verifyStorageCall('set', {
        'newtab-tool-launch': expect.objectContaining({
          toolId: 'api-diff',
          data: { config: 'test' },
          source: 'popup'
        })
      });
      
      // 验证Tabs调用
      verifyTabsCall('create', {
        url: 'chrome-extension://mock-extension-id/newtab.html',
        active: true
      });
      
      // 验证通知调用
      expect(mockNotificationManager.info).toHaveBeenCalledWith('正在新标签页中打开 API Diff工具...');
      
      // 验证工具action没有被直接调用
      expect(newtabTool.action).not.toHaveBeenCalled();
    });

    it('应该正确处理Storage失败并回退到popup执行', async () => {
      // 准备测试数据
      const newtabTool = new MockTool('api-diff', 'API Diff工具', 'newtab');
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(newtabTool);
      
      // 模拟Storage失败
      mockBrowser.storage.local.set.mockRejectedValue(new Error('Storage quota exceeded'));
      
      // 执行测试
      await toolManager.executeTool(newtabTool);
      
      // 验证回退执行
      expect(newtabTool.action).toHaveBeenCalled();
      expect(mockNotificationManager.error).toHaveBeenCalledWith('打开新标签页失败，请重试');
    });

    it('应该正确处理Tabs创建失败并回退到popup执行', async () => {
      // 准备测试数据
      const newtabTool = new MockTool('api-diff', 'API Diff工具', 'newtab');
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(newtabTool);
      
      // 模拟Tabs创建失败
      mockBrowser.tabs.create.mockRejectedValue(new Error('Cannot create tab'));
      
      // 执行测试
      await toolManager.executeTool(newtabTool);
      
      // 验证回退执行
      expect(newtabTool.action).toHaveBeenCalled();
      expect(mockNotificationManager.error).toHaveBeenCalledWith('打开新标签页失败，请重试');
    });
  });

  describe('executeTool方法 - 错误处理', () => {
    it('应该正确处理工具不存在的情况', async () => {
      // 准备测试数据
      const nonExistentTool = new MockTool('non-existent', '不存在的工具');
      
      // 设置ToolRegistry Mock返回null
      mockToolRegistry.getById.mockReturnValue(null);
      
      // 执行测试并验证异常
      await expect(toolManager.executeTool(nonExistentTool)).rejects.toThrow('工具 不存在的工具 未找到');
      
      // 验证错误处理
      expect(mockNotificationManager.error).toHaveBeenCalledWith('执行工具失败: 工具 不存在的工具 未找到');
    });

    it('应该正确处理工具action方法不存在的情况', async () => {
      // 准备测试数据
      const invalidTool = new MockTool('invalid', '无效工具', 'popup');
      invalidTool.action = undefined as any;
      
      // 设置ToolRegistry Mock返回
      mockToolRegistry.getById.mockReturnValue(invalidTool);
      
      // 执行测试并验证异常
      await expect(toolManager.executeTool(invalidTool)).rejects.toThrow('工具 无效工具 的 action 方法不存在');
    });
  });
});
