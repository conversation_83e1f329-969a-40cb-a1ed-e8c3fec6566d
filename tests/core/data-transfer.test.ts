/**
 * NewTab数据传递机制测试
 * 测试popup到newtab的数据传递逻辑
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { setupBrowserMock, clearBrowserMock, mockBrowser } from '../web-standalone/browser-api-mock';

// NewTabLaunchData接口定义
interface NewTabLaunchData {
  toolId: string;
  timestamp: number;
  data: any;
  source: 'popup' | 'newtab' | 'background';
}

// 数据传递工具类
class DataTransferManager {
  /**
   * 创建NewTab启动数据
   */
  static createLaunchData(toolId: string, data?: any, source: 'popup' | 'newtab' | 'background' = 'popup'): NewTabLaunchData {
    return {
      toolId,
      timestamp: Date.now(),
      data: data || {},
      source
    };
  }

  /**
   * 保存启动数据到Storage
   */
  static async saveLaunchData(launchData: NewTabLaunchData): Promise<void> {
    await mockBrowser.storage.local.set({
      'newtab-tool-launch': launchData
    });
  }

  /**
   * 从Storage读取启动数据
   */
  static async loadLaunchData(): Promise<NewTabLaunchData | null> {
    const result = await mockBrowser.storage.local.get('newtab-tool-launch');
    return result['newtab-tool-launch'] || null;
  }

  /**
   * 清理启动数据
   */
  static async clearLaunchData(): Promise<void> {
    await mockBrowser.storage.local.remove('newtab-tool-launch');
  }

  /**
   * 验证启动数据的完整性
   */
  static validateLaunchData(data: any): data is NewTabLaunchData {
    return (
      data &&
      typeof data.toolId === 'string' &&
      typeof data.timestamp === 'number' &&
      data.data !== undefined &&
      ['popup', 'newtab', 'background'].includes(data.source)
    );
  }
}

describe('NewTab数据传递机制测试', () => {
  beforeEach(() => {
    setupBrowserMock();
  });

  afterEach(() => {
    clearBrowserMock();
  });

  describe('createLaunchData方法', () => {
    it('应该正确构造基础NewTabLaunchData', () => {
      const toolId = 'test-tool';
      const launchData = DataTransferManager.createLaunchData(toolId);

      expect(launchData.toolId).toBe('test-tool');
      expect(launchData.timestamp).toBeTypeOf('number');
      expect(launchData.data).toEqual({});
      expect(launchData.source).toBe('popup');
    });

    it('应该正确构造带数据的NewTabLaunchData', () => {
      const toolId = 'api-diff';
      const testData = { config: 'test', options: { debug: true } };
      
      const launchData = DataTransferManager.createLaunchData(toolId, testData);

      expect(launchData.toolId).toBe('api-diff');
      expect(launchData.data).toEqual(testData);
      expect(launchData.source).toBe('popup');
    });

    it('应该正确设置不同的source', () => {
      const toolId = 'test-tool';
      
      const popupData = DataTransferManager.createLaunchData(toolId, {}, 'popup');
      const newtabData = DataTransferManager.createLaunchData(toolId, {}, 'newtab');
      const backgroundData = DataTransferManager.createLaunchData(toolId, {}, 'background');

      expect(popupData.source).toBe('popup');
      expect(newtabData.source).toBe('newtab');
      expect(backgroundData.source).toBe('background');
    });

    it('应该生成不同的timestamp', async () => {
      const data1 = DataTransferManager.createLaunchData('tool1');
      
      // 等待1ms确保时间戳不同
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const data2 = DataTransferManager.createLaunchData('tool2');

      expect(data2.timestamp).toBeGreaterThan(data1.timestamp);
    });
  });

  describe('saveLaunchData和loadLaunchData方法', () => {
    it('应该正确保存和读取启动数据', async () => {
      const originalData = DataTransferManager.createLaunchData('test-tool', { key: 'value' });

      // 保存数据
      await DataTransferManager.saveLaunchData(originalData);

      // 读取数据
      const loadedData = await DataTransferManager.loadLaunchData();

      expect(loadedData).toEqual(originalData);
    });

    it('应该在没有数据时返回null', async () => {
      const loadedData = await DataTransferManager.loadLaunchData();
      expect(loadedData).toBeNull();
    });

    it('应该正确处理复杂数据结构', async () => {
      const complexData = {
        config: {
          endpoints: ['http://api1.com', 'http://api2.com'],
          headers: { 'Content-Type': 'application/json' },
          timeout: 5000
        },
        userPreferences: {
          theme: 'dark',
          language: 'zh-CN'
        },
        metadata: {
          version: '1.0.0',
          lastModified: new Date().toISOString()
        }
      };

      const launchData = DataTransferManager.createLaunchData('api-diff', complexData);

      // 保存和读取
      await DataTransferManager.saveLaunchData(launchData);
      const loadedData = await DataTransferManager.loadLaunchData();

      expect(loadedData?.data).toEqual(complexData);
    });
  });

  describe('clearLaunchData方法', () => {
    it('应该正确清理启动数据', async () => {
      // 先保存数据
      const launchData = DataTransferManager.createLaunchData('test-tool');
      await DataTransferManager.saveLaunchData(launchData);

      // 验证数据存在
      let loadedData = await DataTransferManager.loadLaunchData();
      expect(loadedData).not.toBeNull();

      // 清理数据
      await DataTransferManager.clearLaunchData();

      // 验证数据已清理
      loadedData = await DataTransferManager.loadLaunchData();
      expect(loadedData).toBeNull();
    });
  });

  describe('validateLaunchData方法', () => {
    it('应该验证有效的启动数据', () => {
      const validData = {
        toolId: 'test-tool',
        timestamp: Date.now(),
        data: { key: 'value' },
        source: 'popup' as const
      };

      expect(DataTransferManager.validateLaunchData(validData)).toBe(true);
    });

    it('应该拒绝无效的启动数据', () => {
      const invalidCases = [
        null,
        undefined,
        {},
        { toolId: 'test' }, // 缺少其他字段
        { toolId: 123, timestamp: Date.now(), data: {}, source: 'popup' }, // toolId类型错误
        { toolId: 'test', timestamp: 'invalid', data: {}, source: 'popup' }, // timestamp类型错误
        { toolId: 'test', timestamp: Date.now(), source: 'popup' }, // 缺少data字段
        { toolId: 'test', timestamp: Date.now(), data: {}, source: 'invalid' }, // source值无效
      ];

      invalidCases.forEach((invalidData, index) => {
        expect(DataTransferManager.validateLaunchData(invalidData)).toBe(false);
      });
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该正确处理大数据量', async () => {
      // 创建大数据对象
      const largeData = {
        largeArray: new Array(10000).fill('test-data'),
        largeString: 'x'.repeat(100000),
        nestedObject: {}
      };

      // 创建嵌套对象
      let current = largeData.nestedObject;
      for (let i = 0; i < 100; i++) {
        current.nested = { level: i };
        current = current.nested;
      }

      const launchData = DataTransferManager.createLaunchData('large-data-tool', largeData);

      // 保存和读取
      await DataTransferManager.saveLaunchData(launchData);
      const loadedData = await DataTransferManager.loadLaunchData();

      expect(loadedData?.data.largeArray).toHaveLength(10000);
      expect(loadedData?.data.largeString).toHaveLength(100000);
    });

    it('应该正确处理特殊字符', async () => {
      const specialData = {
        unicode: '🚀 测试 Unicode 字符 🎉',
        quotes: 'Single \'quotes\' and "double quotes"',
        escapes: 'Line\nBreaks\tTabs\\Backslashes',
        json: '{"nested": "json", "array": [1, 2, 3]}',
        html: '<div class="test">HTML content</div>'
      };

      const launchData = DataTransferManager.createLaunchData('special-chars-tool', specialData);

      // 保存和读取
      await DataTransferManager.saveLaunchData(launchData);
      const loadedData = await DataTransferManager.loadLaunchData();

      expect(loadedData?.data).toEqual(specialData);
    });

    it('应该正确处理null和undefined值', async () => {
      const nullData = {
        nullValue: null,
        undefinedValue: undefined,
        emptyString: '',
        emptyArray: [],
        emptyObject: {}
      };

      const launchData = DataTransferManager.createLaunchData('null-data-tool', nullData);

      // 保存和读取
      await DataTransferManager.saveLaunchData(launchData);
      const loadedData = await DataTransferManager.loadLaunchData();

      // 注意：JSON序列化会将undefined转换为null或忽略
      expect(loadedData?.data.nullValue).toBeNull();
      expect(loadedData?.data.emptyString).toBe('');
      expect(loadedData?.data.emptyArray).toEqual([]);
      expect(loadedData?.data.emptyObject).toEqual({});
    });
  });

  describe('实际使用场景模拟', () => {
    it('应该模拟完整的popup到newtab数据传递流程', async () => {
      // 1. Popup阶段：用户点击工具，准备数据
      const toolConfig = {
        endpoints: {
          primary: 'https://api.example.com/v1',
          secondary: 'https://api-backup.example.com/v1'
        },
        headers: {
          'Authorization': 'Bearer token123',
          'Content-Type': 'application/json'
        },
        options: {
          timeout: 10000,
          retries: 3
        }
      };

      const launchData = DataTransferManager.createLaunchData('api-diff', toolConfig, 'popup');

      // 2. 保存数据到Storage
      await DataTransferManager.saveLaunchData(launchData);

      // 3. NewTab阶段：读取数据
      const receivedData = await DataTransferManager.loadLaunchData();

      // 4. 验证数据完整性
      expect(DataTransferManager.validateLaunchData(receivedData)).toBe(true);
      expect(receivedData?.toolId).toBe('api-diff');
      expect(receivedData?.data).toEqual(toolConfig);
      expect(receivedData?.source).toBe('popup');

      // 5. 清理数据（模拟NewTab页面处理完成）
      await DataTransferManager.clearLaunchData();

      // 6. 验证数据已清理
      const clearedData = await DataTransferManager.loadLaunchData();
      expect(clearedData).toBeNull();
    });
  });
});
