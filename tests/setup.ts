/**
 * Test Setup
 * 测试环境的全局设置和模拟
 */

import { vi } from 'vitest';

// Mock browser APIs
const mockBrowser = {
  storage: {
    local: {
      get: vi.fn().mockResolvedValue({}),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    },
    sync: {
      get: vi.fn().mockResolvedValue({}),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    }
  },
  runtime: {
    getURL: vi.fn((path: string) => `chrome-extension://test-id${path}`),
    getManifest: vi.fn().mockReturnValue({
      version: '1.0.0',
      name: 'Test Extension'
    }),
    onMessage: {
      addListener: vi.fn(),
      removeListener: vi.fn()
    },
    sendMessage: vi.fn().mockResolvedValue(undefined)
  },
  tabs: {
    create: vi.fn().mockResolvedValue({ id: 1, url: 'test-url' }),
    query: vi.fn().mockResolvedValue([]),
    update: vi.fn().mockResolvedValue(undefined),
    remove: vi.fn().mockResolvedValue(undefined)
  },
  notifications: {
    create: vi.fn().mockResolvedValue('test-notification-id'),
    clear: vi.fn().mockResolvedValue(true)
  }
};

// Set up global mocks
globalThis.mockBrowser = mockBrowser;
globalThis.mockChrome = mockBrowser;
globalThis.browser = mockBrowser;
globalThis.chrome = mockBrowser;

// Mock fetch API
globalThis.fetch = vi.fn();

// Mock DOM APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn().mockResolvedValue(undefined),
    readText: vi.fn().mockResolvedValue('')
  }
});

// Mock URL constructor for testing
globalThis.URL = class MockURL {
  href: string;
  origin: string;
  protocol: string;
  hostname: string;
  pathname: string;
  search: string;
  searchParams: URLSearchParams;

  constructor(url: string, base?: string) {
    this.href = url;
    this.origin = 'https://example.com';
    this.protocol = 'https:';
    this.hostname = 'example.com';
    this.pathname = '/';
    this.search = '';
    this.searchParams = new URLSearchParams();
  }

  toString() {
    return this.href;
  }
} as any;

// Mock Blob for file operations
globalThis.Blob = class MockBlob {
  size: number;
  type: string;
  
  constructor(parts: any[], options: any = {}) {
    this.size = 0;
    this.type = options.type || '';
  }
} as any;

// Mock File for file operations
globalThis.File = class MockFile extends globalThis.Blob {
  name: string;
  lastModified: number;
  
  constructor(parts: any[], name: string, options: any = {}) {
    super(parts, options);
    this.name = name;
    this.lastModified = Date.now();
  }
} as any;

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
