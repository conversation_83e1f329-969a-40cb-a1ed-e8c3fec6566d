# API Diff Tool Tests

本目录包含 API Diff Tool 的完整测试套件，使用 Vitest 测试框架。

## 🧪 测试框架

- **测试运行器**: Vitest
- **断言库**: Vitest 内置断言
- **模拟库**: Vitest 内置 vi
- **DOM 环境**: jsdom
- **覆盖率**: v8

## 📁 测试结构

```
tests/
├── setup.ts                           # 测试环境设置
├── api-diff/                          # API Diff 工具测试
│   ├── validation-utils.test.ts       # 验证工具测试
│   ├── http-request-utils.test.ts     # HTTP 请求工具测试
│   ├── curl-parser.test.ts            # Curl 解析器测试
│   ├── diff-utils.test.ts             # 差异计算工具测试
│   ├── config-manager.test.ts         # 配置管理器测试
│   └── api-diff-tool.test.ts          # 主工具类测试
└── README.md                          # 本文档
```

## 🚀 运行测试

### 安装依赖

```bash
npm install
```

### 运行所有测试

```bash
npm test
```

### 运行测试（单次）

```bash
npm run test:run
```

### 运行测试并生成覆盖率报告

```bash
npm run test:coverage
```

### 运行测试 UI 界面

```bash
npm run test:ui
```

### 监视模式运行测试

```bash
npm run test:watch
```

## 📊 测试覆盖的组件

### 1. ValidationUtils (validation-utils.test.ts)
- URL 验证
- JSON 验证
- HTTP 方法与请求体兼容性验证
- 请求头验证
- 查询参数验证
- 认证信息验证
- 超时时间验证
- 完整请求配置验证

### 2. HttpRequestUtils (http-request-utils.test.ts)
- 请求选项构建
- URL 构建（包含查询参数）
- 请求执行
- 响应解析
- 错误处理
- 超时处理
- 认证处理

### 3. CurlParser (curl-parser.test.ts)
- 基础 curl 命令解析
- HTTP 方法提取
- 请求头解析
- 请求体解析（JSON、表单数据、URL 编码）
- 认证信息解析
- 查询参数解析
- 复杂 curl 命令解析

### 4. DiffUtils (diff-utils.test.ts)
- 对象差异计算
- 嵌套对象处理
- 数组差异检测
- 字段忽略功能
- 差异格式化
- 差异摘要生成
- 深度相等比较

### 5. ConfigManager (config-manager.test.ts)
- 配置保存和加载
- 配置导入导出
- 执行历史管理
- 设置管理
- 数据清理
- 错误处理

### 6. ApiDiffTool (api-diff-tool.test.ts)
- 工具元数据验证
- 生命周期管理
- 组件集成
- 事件处理
- 状态管理
- 错误处理
- 资源清理

## 🔧 测试配置

### vitest.config.ts
- 全局测试设置
- jsdom 环境配置
- 路径别名设置
- 覆盖率配置
- 浏览器 API 模拟

### tests/setup.ts
- 浏览器扩展 API 模拟
- DOM API 模拟
- 全局变量设置
- 测试前置条件

## 📝 编写测试的最佳实践

### 1. 测试结构
```typescript
describe('ComponentName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Arrange
      const input = 'test input';
      
      // Act
      const result = component.method(input);
      
      // Assert
      expect(result).toBe('expected output');
    });
  });
});
```

### 2. 模拟外部依赖
```typescript
beforeEach(() => {
  vi.clearAllMocks();
  globalThis.fetch = vi.fn();
});
```

### 3. 测试异步代码
```typescript
it('should handle async operations', async () => {
  const promise = asyncFunction();
  await expect(promise).resolves.toBe('expected result');
});
```

### 4. 测试错误情况
```typescript
it('should handle errors gracefully', async () => {
  mockFunction.mockRejectedValue(new Error('Test error'));
  
  const result = await functionUnderTest();
  
  expect(result.success).toBe(false);
  expect(result.error).toContain('Test error');
});
```

## 🎯 测试覆盖率目标

- **语句覆盖率**: > 90%
- **分支覆盖率**: > 85%
- **函数覆盖率**: > 95%
- **行覆盖率**: > 90%

## 🐛 调试测试

### 1. 使用 console.log
```typescript
it('should debug test', () => {
  console.log('Debug info:', testData);
  expect(result).toBe(expected);
});
```

### 2. 使用 Vitest UI
```bash
npm run test:ui
```

### 3. 运行单个测试文件
```bash
npx vitest run tests/api-diff/validation-utils.test.ts
```

### 4. 运行特定测试
```bash
npx vitest run -t "should validate correct URLs"
```

## 📚 相关文档

- [Vitest 官方文档](https://vitest.dev/)
- [jsdom 文档](https://github.com/jsdom/jsdom)
- [测试最佳实践](https://testing-library.com/docs/guiding-principles/)

## 🔄 持续集成

测试应该在以下情况下运行：
- 代码提交前
- Pull Request 创建时
- 主分支合并前
- 发布版本前

建议的 CI 命令：
```bash
npm run test:run
npm run test:coverage
```
