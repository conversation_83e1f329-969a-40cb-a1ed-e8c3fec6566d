import { BaseTool } from '../utils/tool-template';

export class ExampleNewTabTool extends BaseTool {
  id = 'example-newtab-tool';
  name = 'New Tab 工具示例';
  description = '一个在新标签页中运行的工具示例，展示全屏功能';
  icon = '🚀';
  categories = ['all', 'development'];
  displayMode = 'newtab' as const;  // 关键：指定为newtab模式
  requiresFullscreen = true;
  
  // 传递给newtab的数据
  newtabData = {
    initialConfig: {
      theme: 'dark',
      layout: 'advanced'
    },
    permissions: ['clipboard', 'storage']
  };

  async action(): Promise<void> {
    try {
      console.log('🎬 NewTab工具开始执行');
      
      // 检查是否在newtab环境中
      if (!this.isInNewTabEnvironment()) {
        throw new Error('此工具必须在New Tab环境中运行');
      }
      
      // 创建全屏界面
      await this.createFullscreenInterface();
      
      // 初始化工具功能
      await this.initializeFeatures();
      
      console.log('✅ NewTab工具执行完成');
      
    } catch (error) {
      console.error('❌ NewTab工具执行失败:', error);
      throw error;
    }
  }
  
  async onNewTabInit(): Promise<void> {
    console.log('🔧 NewTab环境初始化');
    
    // 初始化全屏特定的资源
    await this.loadFullscreenResources();
    
    // 设置事件监听器
    this.setupFullscreenEventListeners();
  }
  
  async onNewTabDestroy(): Promise<void> {
    console.log('🧹 NewTab环境清理');
    
    // 清理资源
    await this.cleanupResources();
    
    // 移除事件监听器
    this.removeEventListeners();
  }
  
  private isInNewTabEnvironment(): boolean {
    // 检查当前是否在newtab页面中
    return window.location.pathname.includes('newtab.html');
  }
  
  private async createFullscreenInterface(): Promise<void> {
    const container = document.getElementById('tool-container');
    if (!container) return;
    
    container.innerHTML = `
      <div class="fullscreen-tool">
        <header class="tool-header">
          <h1>${this.icon} ${this.name}</h1>
          <div class="tool-controls">
            <button id="minimize-btn" class="control-btn">最小化</button>
            <button id="close-btn" class="control-btn close">关闭</button>
          </div>
        </header>
        
        <main class="tool-main">
          <div class="workspace">
            <!-- 工具主要功能区域 -->
            <div class="tool-content">
              <h2>🎯 全屏工作区</h2>
              <p>这是一个全屏工具的示例界面，展示了New Tab模式的强大功能</p>
              
              <div class="tool-features">
                <div class="feature-card">
                  <h3>🎯 功能一</h3>
                  <p>复杂的数据分析和可视化</p>
                  <button class="feature-btn" onclick="alert('功能一被点击')">体验功能</button>
                </div>
                
                <div class="feature-card">
                  <h3>📊 功能二</h3>
                  <p>实时数据处理和图表展示</p>
                  <button class="feature-btn" onclick="alert('功能二被点击')">体验功能</button>
                </div>
                
                <div class="feature-card">
                  <h3>⚡ 功能三</h3>
                  <p>高性能计算和批量操作</p>
                  <button class="feature-btn" onclick="alert('功能三被点击')">体验功能</button>
                </div>
              </div>
              
              <div class="demo-area">
                <h3>🛠️ 演示区域</h3>
                <textarea id="demo-input" placeholder="在这里输入一些文本来测试功能..." rows="4"></textarea>
                <div class="demo-buttons">
                  <button id="copy-btn" class="demo-btn">复制文本</button>
                  <button id="clear-btn" class="demo-btn">清空文本</button>
                  <button id="transform-btn" class="demo-btn">转换大写</button>
                </div>
                <div id="demo-output" class="demo-output"></div>
              </div>
            </div>
          </div>
        </main>
        
        <footer class="tool-footer">
          <div class="status-bar">
            <span class="status-indicator ready">✅ 就绪</span>
            <span class="memory-usage">内存: 45MB</span>
            <span class="performance">性能: 优秀</span>
            <span class="timestamp">启动时间: ${new Date().toLocaleTimeString()}</span>
          </div>
        </footer>
      </div>
    `;
    
    // 添加样式
    this.addToolStyles();
    
    // 添加事件监听器
    this.setupToolControls();
  }
  
  private addToolStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .fullscreen-tool {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      .tool-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        background: rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
      }
      
      .tool-header h1 {
        margin: 0;
        font-size: 1.5rem;
      }
      
      .tool-controls {
        display: flex;
        gap: 0.5rem;
      }
      
      .control-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.25rem;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .control-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      
      .control-btn.close {
        background: rgba(239, 68, 68, 0.8);
      }
      
      .tool-main {
        flex: 1;
        padding: 2rem;
        overflow-y: auto;
      }
      
      .tool-content h2 {
        margin-bottom: 1rem;
        text-align: center;
      }
      
      .tool-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
      }
      
      .feature-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-radius: 0.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      .feature-btn, .demo-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        cursor: pointer;
        margin-top: 1rem;
        transition: all 0.2s;
      }
      
      .feature-btn:hover, .demo-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }
      
      .demo-area {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-top: 2rem;
      }
      
      #demo-input {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 0.25rem;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        margin-bottom: 1rem;
      }
      
      .demo-buttons {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
      }
      
      .demo-output {
        background: rgba(0, 0, 0, 0.3);
        padding: 1rem;
        border-radius: 0.25rem;
        min-height: 3rem;
        font-family: monospace;
      }
      
      .tool-footer {
        background: rgba(0, 0, 0, 0.2);
        padding: 0.75rem 2rem;
      }
      
      .status-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
      }
      
      .status-indicator.ready {
        color: #10b981;
      }
    `;
    document.head.appendChild(style);
  }
  
  private setupToolControls(): void {
    const minimizeBtn = document.getElementById('minimize-btn');
    const closeBtn = document.getElementById('close-btn');
    const copyBtn = document.getElementById('copy-btn');
    const clearBtn = document.getElementById('clear-btn');
    const transformBtn = document.getElementById('transform-btn');
    
    minimizeBtn?.addEventListener('click', () => {
      this.minimizeTool();
    });
    
    closeBtn?.addEventListener('click', () => {
      this.closeTool();
    });
    
    copyBtn?.addEventListener('click', () => {
      this.copyDemoText();
    });
    
    clearBtn?.addEventListener('click', () => {
      this.clearDemoText();
    });
    
    transformBtn?.addEventListener('click', () => {
      this.transformDemoText();
    });
  }
  
  private minimizeTool(): void {
    // 返回工具选择界面
    document.dispatchEvent(new CustomEvent('show-tool-selector'));
    location.reload();
  }
  
  private closeTool(): void {
    // 关闭当前标签页
    window.close();
  }
  
  private async copyDemoText(): Promise<void> {
    const input = document.getElementById('demo-input') as HTMLTextAreaElement;
    const output = document.getElementById('demo-output');
    
    if (input && output) {
      try {
        await navigator.clipboard.writeText(input.value);
        output.textContent = '✅ 文本已复制到剪贴板';
      } catch (error) {
        output.textContent = '❌ 复制失败: ' + error;
      }
    }
  }
  
  private clearDemoText(): void {
    const input = document.getElementById('demo-input') as HTMLTextAreaElement;
    const output = document.getElementById('demo-output');
    
    if (input && output) {
      input.value = '';
      output.textContent = '🧹 文本已清空';
    }
  }
  
  private transformDemoText(): void {
    const input = document.getElementById('demo-input') as HTMLTextAreaElement;
    const output = document.getElementById('demo-output');
    
    if (input && output) {
      const transformed = input.value.toUpperCase();
      input.value = transformed;
      output.textContent = '🔄 文本已转换为大写';
    }
  }
  
  private async initializeFeatures(): Promise<void> {
    // 初始化工具的各种功能
    console.log('🔧 初始化工具功能...');
    
    // 模拟功能初始化
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('✅ 工具功能初始化完成');
  }
  
  private async loadFullscreenResources(): Promise<void> {
    // 加载全屏特定的资源
    console.log('📦 加载全屏资源...');
  }
  
  private setupFullscreenEventListeners(): void {
    // 设置全屏特定的事件监听器
    console.log('🎧 设置全屏事件监听器...');
  }
  
  private async cleanupResources(): Promise<void> {
    // 清理资源
    console.log('🧹 清理资源...');
  }
  
  private removeEventListeners(): void {
    // 移除事件监听器
    console.log('🔇 移除事件监听器...');
  }
}
