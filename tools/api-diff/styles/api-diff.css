/**
 * API Diff Tool Styles
 * 接口差异对比工具的样式定义
 */

/* 全局重置和基础样式 */
.api-diff-tool * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.api-diff-tool {
  font-family: var(--font-family-base, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
  background: var(--color-background-primary, #f5f5f5);
  color: var(--color-text-primary, #333);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 工具头部 */
.tool-header {
  background: var(--color-background-secondary, white);
  border-bottom: 1px solid var(--color-border-primary, #e0e0e0);
  padding: var(--spacing-md, 16px) var(--spacing-lg, 24px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.tool-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 12px);
}

.tool-icon {
  font-size: var(--font-size-xl, 24px);
}

.tool-title h1 {
  font-size: var(--font-size-lg, 20px);
  font-weight: var(--font-weight-semibold, 600);
  color: var(--color-text-primary, #333);
}

.header-actions {
  display: flex;
  gap: var(--spacing-xs, 8px);
}

.header-btn {
  padding: var(--spacing-xs, 8px) var(--spacing-md, 16px);
  border: 1px solid var(--color-border-secondary, #ddd);
  background: var(--color-background-secondary, white);
  border-radius: var(--border-radius-md, 6px);
  cursor: pointer;
  font-size: var(--font-size-sm, 14px);
  color: var(--color-text-secondary, #666);
  transition: all 0.2s ease;
}

.header-btn:hover {
  background: var(--color-background-hover, #f8f9fa);
  border-color: var(--color-border-hover, #ccc);
  color: var(--color-text-primary, #333);
}

.header-btn:active {
  transform: translateY(1px);
}

/* 工具内容区域 */
.tool-content {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-lg, 24px);
  width: 100%;
}

/* 请求配置区域 */
.request-section {
  background: var(--color-background-secondary, white);
  border-radius: var(--border-radius-lg, 8px);
  padding: var(--spacing-lg, 24px);
  margin-bottom: var(--spacing-lg, 24px);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0,0,0,0.1));
  border: 1px solid var(--color-border-primary, #e9ecef);
}

/* 结果区域 */
.results-section {
  background: var(--color-background-secondary, white);
  border-radius: var(--border-radius-lg, 8px);
  padding: var(--spacing-lg, 24px);
  box-shadow: var(--shadow-sm, 0 2px 4px rgba(0,0,0,0.1));
  border: 1px solid var(--color-border-primary, #e9ecef);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg, 20px);
  padding-bottom: var(--spacing-md, 16px);
  border-bottom: 1px solid var(--color-border-primary, #e9ecef);
}

.section-header h2 {
  font-size: var(--font-size-lg, 18px);
  font-weight: var(--font-weight-semibold, 600);
  color: var(--color-text-primary, #333);
}

/* 结果标签 */
.results-tabs {
  display: flex;
  gap: var(--spacing-xs, 8px);
  margin-bottom: var(--spacing-lg, 20px);
}

.result-tab {
  padding: var(--spacing-xs, 8px) var(--spacing-md, 16px);
  border: 1px solid var(--color-border-secondary, #ddd);
  background: var(--color-background-secondary, white);
  border-radius: var(--border-radius-md, 6px);
  cursor: pointer;
  font-size: var(--font-size-sm, 14px);
  color: var(--color-text-secondary, #666);
  transition: all 0.2s ease;
  position: relative;
}

.result-tab:hover:not(.active) {
  background: var(--color-background-hover, #f8f9fa);
  color: var(--color-text-primary, #333);
}

.result-tab.active {
  background: var(--color-primary, #007bff);
  color: white;
  border-color: var(--color-primary, #007bff);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* 结果内容 */
.results-content {
  position: relative;
  min-height: 400px;
}

.result-panel {
  display: none;
  animation: fadeIn 0.3s ease;
}

.result-panel.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 工具底部 */
.tool-footer {
  background: var(--color-background-secondary, white);
  border-top: 1px solid var(--color-border-primary, #e0e0e0);
  padding: var(--spacing-sm, 12px) var(--spacing-lg, 24px);
  margin-top: auto;
}

.status-bar {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm, 14px);
  color: var(--color-text-secondary, #666);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: var(--spacing-lg, 20px);
  right: var(--spacing-lg, 20px);
  min-width: 300px;
  max-width: 500px;
  padding: var(--spacing-md, 16px);
  border-radius: var(--border-radius-lg, 8px);
  color: white;
  font-size: var(--font-size-sm, 14px);
  z-index: 10000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  box-shadow: var(--shadow-lg, 0 4px 12px rgba(0,0,0,0.15));
  backdrop-filter: blur(10px);
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs, 4px);
}

.notification-title {
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--font-size-base, 16px);
}

.notification-message {
  opacity: 0.9;
  line-height: 1.4;
}

.notification-close {
  position: absolute;
  top: var(--spacing-xs, 8px);
  right: var(--spacing-xs, 8px);
  background: none;
  border: none;
  color: white;
  font-size: var(--font-size-lg, 18px);
  cursor: pointer;
  padding: var(--spacing-xs, 4px);
  border-radius: var(--border-radius-sm, 4px);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.notification-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.notification-success {
  background: linear-gradient(135deg, var(--color-success, #28a745), #20c997);
}

.notification-error {
  background: linear-gradient(135deg, var(--color-error, #dc3545), #e74c3c);
}

.notification-info {
  background: linear-gradient(135deg, var(--color-info, #17a2b8), #3498db);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border-primary, #e9ecef);
  border-top: 3px solid var(--color-primary, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .tool-content {
    padding: var(--spacing-md, 16px);
  }
  
  .header-content {
    padding: 0 var(--spacing-md, 16px);
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md, 16px);
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .tool-content {
    padding: var(--spacing-sm, 12px);
  }
  
  .request-section,
  .results-section {
    padding: var(--spacing-md, 16px);
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md, 16px);
  }
  
  .results-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .notification {
    left: var(--spacing-sm, 12px);
    right: var(--spacing-sm, 12px);
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .tool-title h1 {
    font-size: var(--font-size-base, 16px);
  }
  
  .header-btn {
    padding: var(--spacing-xs, 6px) var(--spacing-sm, 12px);
    font-size: var(--font-size-xs, 12px);
  }
  
  .result-tab {
    padding: var(--spacing-xs, 6px) var(--spacing-sm, 12px);
    font-size: var(--font-size-xs, 12px);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .api-diff-tool {
    background: var(--color-background-primary-dark, #1a1a1a);
    color: var(--color-text-primary-dark, #e0e0e0);
  }
  
  .tool-header,
  .request-section,
  .results-section,
  .tool-footer {
    background: var(--color-background-secondary-dark, #2d2d2d);
    border-color: var(--color-border-primary-dark, #404040);
  }
  
  .header-btn,
  .result-tab {
    background: var(--color-background-secondary-dark, #2d2d2d);
    border-color: var(--color-border-secondary-dark, #555);
    color: var(--color-text-secondary-dark, #ccc);
  }
  
  .header-btn:hover,
  .result-tab:hover:not(.active) {
    background: var(--color-background-hover-dark, #3a3a3a);
    color: var(--color-text-primary-dark, #e0e0e0);
  }
  
  .section-header {
    border-color: var(--color-border-primary-dark, #404040);
  }
  
  .loading-overlay {
    background: rgba(45, 45, 45, 0.8);
  }
  
  .loading-spinner {
    border-color: var(--color-border-primary-dark, #404040);
    border-top-color: var(--color-primary, #007bff);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .api-diff-tool {
    border: 2px solid;
  }
  
  .header-btn,
  .result-tab {
    border-width: 2px;
  }
  
  .result-tab.active {
    border-width: 3px;
  }
  
  .notification {
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .api-diff-tool *,
  .api-diff-tool *::before,
  .api-diff-tool *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: transparent;
  }
}
