/**
 * cURL Parser Component
 * cURL 导入组件，提供 cURL 命令解析和配置自动填充功能
 */

import type { 
  RequestConfig, 
  ParseResult 
} from '../types/api-diff-types';
import { CurlParsingUtils } from '../utils/curl-parsing-utils';

export class CurlParser {
  private container: HTMLElement;
  private textArea: HTMLTextAreaElement | null = null;
  private parseButton: HTMLButtonElement | null = null;
  private clearButton: HTMLButtonElement | null = null;
  private resultContainer: HTMLElement | null = null;
  
  // 事件回调
  private onConfigParsed?: (config: RequestConfig) => void;
  private onParseError?: (error: string) => void;

  constructor(container: HTMLElement) {
    this.container = container;
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    this.render();
    this.bindEvents();
  }

  /**
   * 渲染组件界面
   */
  private render(): void {
    this.container.innerHTML = `
      <div class="curl-parser">
        <div class="curl-input-section">
          <div class="input-header">
            <h4>Import from cURL</h4>
            <p class="text-secondary">粘贴 cURL 命令自动解析请求配置</p>
          </div>
          
          <div class="input-area">
            <textarea 
              id="curl-input" 
              class="curl-textarea" 
              placeholder="curl -X POST https://api.example.com/users \\
  -H 'Content-Type: application/json' \\
  -H 'Authorization: Bearer token123' \\
  -d '{\"name\":\"John\",\"email\":\"<EMAIL>\"}'"
              rows="8"
            ></textarea>
          </div>
          
          <div class="input-actions">
            <button id="parse-curl-btn" class="btn btn-primary">
              📥 Parse cURL
            </button>
            <button id="clear-curl-btn" class="btn btn-secondary">
              🗑️ Clear
            </button>
            <button id="example-btn" class="btn btn-outline">
              📋 Examples
            </button>
          </div>
        </div>
        
        <div class="curl-result-section">
          <div id="curl-result" class="curl-result"></div>
        </div>
        
        <div class="curl-help-section">
          <details class="help-details">
            <summary>Supported cURL Options</summary>
            <div class="help-content">
              ${this.renderSupportedOptions()}
            </div>
          </details>
        </div>
      </div>
    `;

    // 获取 DOM 元素引用
    this.textArea = this.container.querySelector('#curl-input');
    this.parseButton = this.container.querySelector('#parse-curl-btn');
    this.clearButton = this.container.querySelector('#clear-curl-btn');
    this.resultContainer = this.container.querySelector('#curl-result');
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 解析按钮
    this.parseButton?.addEventListener('click', () => {
      this.handleParse();
    });

    // 清空按钮
    this.clearButton?.addEventListener('click', () => {
      this.handleClear();
    });

    // 示例按钮
    const exampleButton = this.container.querySelector('#example-btn');
    exampleButton?.addEventListener('click', () => {
      this.showExamples();
    });

    // 文本区域变化
    this.textArea?.addEventListener('input', () => {
      this.handleInputChange();
    });

    // 键盘快捷键
    this.textArea?.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        this.handleParse();
      }
    });
  }

  /**
   * 处理解析操作
   */
  private handleParse(): void {
    if (!this.textArea || !this.resultContainer) return;

    const curlCommand = this.textArea.value.trim();
    
    if (!curlCommand) {
      this.showError('Please enter a cURL command');
      return;
    }

    // 显示解析中状态
    this.showLoading();

    // 延迟执行解析，给用户反馈
    setTimeout(() => {
      try {
        const result = CurlParsingUtils.parse(curlCommand);
        
        if (result.success && result.config) {
          this.showSuccess(result.config);
          this.onConfigParsed?.(result.config as RequestConfig);
        } else {
          this.showError(result.error || 'Unknown parsing error', result.errorPosition);
          this.onParseError?.(result.error || 'Unknown parsing error');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unexpected error occurred';
        this.showError(errorMessage);
        this.onParseError?.(errorMessage);
      }
    }, 100);
  }

  /**
   * 处理清空操作
   */
  private handleClear(): void {
    if (this.textArea) {
      this.textArea.value = '';
    }
    
    if (this.resultContainer) {
      this.resultContainer.innerHTML = '';
    }
    
    this.updateParseButtonState();
  }

  /**
   * 处理输入变化
   */
  private handleInputChange(): void {
    this.updateParseButtonState();
    
    // 清空之前的结果
    if (this.resultContainer) {
      this.resultContainer.innerHTML = '';
    }
  }

  /**
   * 更新解析按钮状态
   */
  private updateParseButtonState(): void {
    if (this.parseButton && this.textArea) {
      const hasContent = this.textArea.value.trim().length > 0;
      this.parseButton.disabled = !hasContent;
    }
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    if (!this.resultContainer) return;

    this.resultContainer.innerHTML = `
      <div class="parse-result loading">
        <div class="loading-indicator">
          <div class="loading-spinner"></div>
          <span>Parsing cURL command...</span>
        </div>
      </div>
    `;
  }

  /**
   * 显示解析成功结果
   */
  private showSuccess(config: Partial<RequestConfig>): void {
    if (!this.resultContainer) return;

    const configPreview = this.generateConfigPreview(config);
    
    this.resultContainer.innerHTML = `
      <div class="parse-result success">
        <div class="result-header">
          <div class="result-icon">✅</div>
          <div class="result-title">Parse Successful</div>
        </div>
        
        <div class="result-content">
          <div class="config-preview">
            <h5>Parsed Configuration:</h5>
            <div class="config-details">
              ${configPreview}
            </div>
          </div>
          
          <div class="result-actions">
            <button id="apply-config-btn" class="btn btn-success">
              ✨ Apply Configuration
            </button>
            <button id="copy-config-btn" class="btn btn-outline">
              📋 Copy as JSON
            </button>
          </div>
        </div>
      </div>
    `;

    // 绑定结果操作按钮
    this.bindResultActions(config);
  }

  /**
   * 显示解析错误
   */
  private showError(error: string, position?: number): void {
    if (!this.resultContainer) return;

    const formattedError = position !== undefined 
      ? CurlParsingUtils.formatParseError(error, position)
      : error;

    this.resultContainer.innerHTML = `
      <div class="parse-result error">
        <div class="result-header">
          <div class="result-icon">❌</div>
          <div class="result-title">Parse Failed</div>
        </div>
        
        <div class="result-content">
          <div class="error-message">
            <p>${formattedError}</p>
          </div>
          
          <div class="error-help">
            <p>Common issues:</p>
            <ul>
              <li>Make sure the command starts with "curl"</li>
              <li>Check for unmatched quotes</li>
              <li>Ensure the URL is valid</li>
              <li>Verify header format: "Name: Value"</li>
            </ul>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 生成配置预览
   */
  private generateConfigPreview(config: Partial<RequestConfig>): string {
    const items: string[] = [];

    if (config.method) {
      items.push(`<div class="config-item"><strong>Method:</strong> ${config.method}</div>`);
    }

    if (config.oldUrl) {
      items.push(`<div class="config-item"><strong>URL:</strong> <code>${config.oldUrl}</code></div>`);
    }

    if (config.headers && Object.keys(config.headers).length > 0) {
      const headerCount = Object.keys(config.headers).length;
      items.push(`<div class="config-item"><strong>Headers:</strong> ${headerCount} header(s)</div>`);
    }

    if (config.body && config.body.content) {
      const contentPreview = config.body.content.length > 50 
        ? config.body.content.substring(0, 50) + '...'
        : config.body.content;
      items.push(`<div class="config-item"><strong>Body:</strong> <code>${contentPreview}</code></div>`);
    }

    if (config.auth) {
      items.push(`<div class="config-item"><strong>Auth:</strong> ${config.auth.type}</div>`);
    }

    return items.join('');
  }

  /**
   * 绑定结果操作按钮
   */
  private bindResultActions(config: Partial<RequestConfig>): void {
    const applyButton = this.resultContainer?.querySelector('#apply-config-btn');
    const copyButton = this.resultContainer?.querySelector('#copy-config-btn');

    applyButton?.addEventListener('click', () => {
      this.onConfigParsed?.(config as RequestConfig);
      this.showAppliedFeedback();
    });

    copyButton?.addEventListener('click', () => {
      this.copyConfigToClipboard(config);
    });
  }

  /**
   * 显示应用成功反馈
   */
  private showAppliedFeedback(): void {
    const applyButton = this.resultContainer?.querySelector('#apply-config-btn');
    if (applyButton) {
      const originalText = applyButton.textContent;
      applyButton.textContent = '✅ Applied!';
      applyButton.setAttribute('disabled', 'true');
      
      setTimeout(() => {
        applyButton.textContent = originalText;
        applyButton.removeAttribute('disabled');
      }, 2000);
    }
  }

  /**
   * 复制配置到剪贴板
   */
  private async copyConfigToClipboard(config: Partial<RequestConfig>): Promise<void> {
    try {
      const jsonString = JSON.stringify(config, null, 2);
      await navigator.clipboard.writeText(jsonString);
      
      const copyButton = this.resultContainer?.querySelector('#copy-config-btn');
      if (copyButton) {
        const originalText = copyButton.textContent;
        copyButton.textContent = '✅ Copied!';
        
        setTimeout(() => {
          copyButton.textContent = originalText;
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  }

  /**
   * 显示示例
   */
  private showExamples(): void {
    const examples = CurlParsingUtils.getExampleCommands();
    const exampleList = examples.map((cmd, index) => 
      `<div class="example-item" data-example="${index}">
        <code>${cmd}</code>
      </div>`
    ).join('');

    // 创建示例模态框或下拉菜单
    const existingModal = document.querySelector('.curl-examples-modal');
    if (existingModal) {
      existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'curl-examples-modal';
    modal.innerHTML = `
      <div class="modal-overlay">
        <div class="modal-content">
          <div class="modal-header">
            <h4>cURL Examples</h4>
            <button class="modal-close">×</button>
          </div>
          <div class="modal-body">
            <p>Click on any example to use it:</p>
            <div class="examples-list">
              ${exampleList}
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 绑定示例点击事件
    modal.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      
      if (target.classList.contains('modal-close') || target.classList.contains('modal-overlay')) {
        modal.remove();
      } else if (target.closest('.example-item')) {
        const exampleIndex = parseInt(target.closest('.example-item')!.getAttribute('data-example')!);
        if (this.textArea) {
          this.textArea.value = examples[exampleIndex];
          this.updateParseButtonState();
        }
        modal.remove();
      }
    });
  }

  /**
   * 渲染支持的选项
   */
  private renderSupportedOptions(): string {
    const options = CurlParsingUtils.getSupportedOptions();
    return `
      <div class="supported-options">
        <p>This parser supports the following cURL options:</p>
        <ul>
          ${options.map(option => `<li><code>${option}</code></li>`).join('')}
        </ul>
      </div>
    `;
  }

  /**
   * 公共方法：设置 cURL 命令
   */
  public setCurlCommand(command: string): void {
    if (this.textArea) {
      this.textArea.value = command;
      this.updateParseButtonState();
    }
  }

  /**
   * 公共方法：获取当前 cURL 命令
   */
  public getCurlCommand(): string {
    return this.textArea?.value || '';
  }

  /**
   * 公共方法：设置事件回调
   */
  public setCallbacks(callbacks: {
    onConfigParsed?: (config: RequestConfig) => void;
    onParseError?: (error: string) => void;
  }): void {
    this.onConfigParsed = callbacks.onConfigParsed;
    this.onParseError = callbacks.onParseError;
  }

  /**
   * 公共方法：清空组件
   */
  public clear(): void {
    this.handleClear();
  }
}
