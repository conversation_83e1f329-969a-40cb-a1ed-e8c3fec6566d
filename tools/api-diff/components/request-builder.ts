/**
 * Request Builder Component
 * 请求构建器组件，提供 Postman 风格的请求配置界面
 */

import type {
  RequestConfig,
  HttpMethod,
  BodyType,
  AuthType,
  CONSTANTS
} from '../types/api-diff-types';
import { ValidationUtils } from '../utils/validation-utils';
import { CurlParser } from './curl-parser';
import { UIUtils } from '../../../utils/ui-components';

export class RequestBuilder {
  private container: HTMLElement;
  private currentConfig: RequestConfig;
  private activeTab: string = 'headers';
  
  // DOM 元素引用
  private methodSelect: HTMLSelectElement | null = null;
  private oldUrlInput: HTMLInputElement | null = null;
  private newUrlInput: HTMLInputElement | null = null;
  private tabButtons: Map<string, HTMLButtonElement> = new Map();
  private tabContents: Map<string, HTMLElement> = new Map();

  // 子组件
  private curlParser: CurlParser | null = null;
  
  // 事件回调
  private onConfigChange?: (config: RequestConfig) => void;
  private onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;

  constructor(container: HTMLElement) {
    this.container = container;
    this.currentConfig = this.getDefaultConfig();
    this.init();
  }

  /**
   * 初始化组件
   */
  private init(): void {
    this.render();
    this.bindEvents();
    this.switchTab('headers'); // 默认显示 Headers tab
  }

  /**
   * 渲染组件界面
   */
  private render(): void {
    this.container.innerHTML = `
      <div class="request-builder">
        <!-- 顶部行：Method、URLs、Actions -->
        <div class="request-top-row">
          <div class="method-selector">
            <select id="method-select" class="form-control">
              ${this.renderMethodOptions()}
            </select>
          </div>
          
          <div class="url-input-group">
            <div class="url-input">
              <label class="form-label">Old API URL</label>
              <input 
                id="old-url-input" 
                type="text" 
                class="form-control" 
                placeholder="https://api-old.example.com/endpoint"
                value="${this.currentConfig.oldUrl}"
              />
            </div>
            <div class="url-input">
              <label class="form-label">New API URL</label>
              <input 
                id="new-url-input" 
                type="text" 
                class="form-control" 
                placeholder="https://api-new.example.com/endpoint"
                value="${this.currentConfig.newUrl}"
              />
            </div>
          </div>
          
          <div class="action-buttons">
            <button id="compare-btn" class="compare-btn" disabled>
              🔄 对比
            </button>
            <button id="settings-btn" class="header-btn">
              ⚙️
            </button>
          </div>
        </div>

        <!-- Tab 导航 -->
        <div class="request-tabs">
          <button class="request-tab" data-tab="headers">Headers</button>
          <button class="request-tab" data-tab="query">Query</button>
          <button class="request-tab" data-tab="body">Body</button>
          <button class="request-tab" data-tab="auth">Auth</button>
          <button class="request-tab" data-tab="curl">cURL Import</button>
        </div>

        <!-- Tab 内容区域 -->
        <div class="request-tab-content">
          <div id="tab-headers" class="tab-pane">
            ${this.renderHeadersTab()}
          </div>
          <div id="tab-query" class="tab-pane">
            ${this.renderQueryTab()}
          </div>
          <div id="tab-body" class="tab-pane">
            ${this.renderBodyTab()}
          </div>
          <div id="tab-auth" class="tab-pane">
            ${this.renderAuthTab()}
          </div>
          <div id="tab-curl" class="tab-pane">
            ${this.renderCurlTab()}
          </div>
        </div>
      </div>
    `;

    // 获取 DOM 元素引用
    this.methodSelect = this.container.querySelector('#method-select');
    this.oldUrlInput = this.container.querySelector('#old-url-input');
    this.newUrlInput = this.container.querySelector('#new-url-input');
    
    // 获取 Tab 按钮和内容引用
    const tabButtons = this.container.querySelectorAll('.request-tab');
    const tabPanes = this.container.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
      const tabName = button.getAttribute('data-tab');
      if (tabName) {
        this.tabButtons.set(tabName, button as HTMLButtonElement);
      }
    });
    
    tabPanes.forEach(pane => {
      const tabName = pane.id.replace('tab-', '');
      this.tabContents.set(tabName, pane as HTMLElement);
    });
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // Method 选择器变化
    this.methodSelect?.addEventListener('change', () => {
      this.currentConfig.method = this.methodSelect!.value as HttpMethod;
      this.validateAndNotify();
    });

    // URL 输入框变化
    this.oldUrlInput?.addEventListener('input', () => {
      this.currentConfig.oldUrl = this.oldUrlInput!.value;
      this.validateField('oldUrl', this.oldUrlInput!);
      this.validateAndNotify();
    });

    this.newUrlInput?.addEventListener('input', () => {
      this.currentConfig.newUrl = this.newUrlInput!.value;
      this.validateField('newUrl', this.newUrlInput!);
      this.validateAndNotify();
    });

    // Tab 切换
    this.tabButtons.forEach((button, tabName) => {
      button.addEventListener('click', () => {
        this.switchTab(tabName);
      });
    });

    // 对比按钮
    const compareBtn = this.container.querySelector('#compare-btn');
    compareBtn?.addEventListener('click', () => {
      this.handleCompareClick();
    });

    // 绑定各 Tab 的事件
    this.bindTabEvents();
  }

  /**
   * 绑定各 Tab 的事件
   */
  private bindTabEvents(): void {
    // Headers Tab 事件
    this.bindKeyValueEvents('headers');

    // Query Tab 事件
    this.bindKeyValueEvents('query');

    // Body Tab 事件
    this.bindBodyEvents();

    // Auth Tab 事件
    this.bindAuthEvents();

    // cURL Tab 事件
    this.bindCurlEvents();
  }

  /**
   * 绑定键值对编辑器事件
   */
  private bindKeyValueEvents(type: 'headers' | 'query'): void {
    const container = this.container.querySelector(`#${type}-rows`);
    if (!container) return;

    // 使用事件委托处理动态添加的行
    container.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      if (target.classList.contains('key-value-input')) {
        this.updateKeyValueData(type);
        this.validateField(type === 'headers' ? 'headers' : 'queryParams', container);
      }
    });

    container.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('remove-btn')) {
        const row = target.closest('.key-value-row');
        if (row && container.children.length > 1) {
          row.remove();
          this.updateKeyValueData(type);
          this.validateField(type === 'headers' ? 'headers' : 'queryParams', container);
        }
      }
    });

    // 添加行按钮
    const addBtn = this.container.querySelector(`#tab-${type} .add-row-btn`);
    addBtn?.addEventListener('click', () => {
      this.addKeyValueRow(type);
    });
  }

  /**
   * 绑定 Body Tab 事件
   */
  private bindBodyEvents(): void {
    const bodyTypeSelect = this.container.querySelector('#body-type-select');
    bodyTypeSelect?.addEventListener('change', () => {
      this.currentConfig.body.type = (bodyTypeSelect as HTMLSelectElement).value as BodyType;
      this.updateBodyContent();
      this.validateAndNotify();
    });

    const bodyTextarea = this.container.querySelector('#body-textarea');
    bodyTextarea?.addEventListener('input', () => {
      this.currentConfig.body.content = (bodyTextarea as HTMLTextAreaElement).value;
      this.validateField('json', bodyTextarea as HTMLTextAreaElement);
      this.validateAndNotify();
    });
  }

  /**
   * 绑定 Auth Tab 事件
   */
  private bindAuthEvents(): void {
    const authTypeSelect = this.container.querySelector('#auth-type-select');
    authTypeSelect?.addEventListener('change', () => {
      const authType = (authTypeSelect as HTMLSelectElement).value;
      if (authType === 'none') {
        this.currentConfig.auth = undefined;
      } else {
        this.currentConfig.auth = {
          type: authType as AuthType,
          credentials: {}
        };
      }
      this.updateAuthContent();
      this.validateAndNotify();
    });
  }

  /**
   * 绑定 cURL Tab 事件
   */
  private bindCurlEvents(): void {
    const curlContainer = this.container.querySelector('#curl-editor');
    if (curlContainer) {
      // 初始化 cURL 解析器组件
      this.curlParser = new CurlParser(curlContainer as HTMLElement);

      // 设置回调
      this.curlParser.setCallbacks({
        onConfigParsed: (config) => {
          this.handleCurlConfigParsed(config);
        },
        onParseError: (error) => {
          console.warn('cURL parse error:', error);
        }
      });
    }
  }

  /**
   * 渲染 HTTP 方法选项
   */
  private renderMethodOptions(): string {
    const methods: HttpMethod[] = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
    return methods.map(method => 
      `<option value="${method}" ${method === this.currentConfig.method ? 'selected' : ''}>${method}</option>`
    ).join('');
  }

  /**
   * 渲染 Headers Tab
   */
  private renderHeadersTab(): string {
    return `
      <div class="key-value-editor" id="headers-editor">
        <div class="editor-header">
          <h4>Request Headers</h4>
          <p class="text-secondary">设置请求头信息</p>
        </div>
        <div class="key-value-rows" id="headers-rows">
          ${this.renderKeyValueRows(this.currentConfig.headers)}
        </div>
        <button class="add-row-btn" onclick="this.addHeaderRow()">
          ➕ Add Header
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Query Tab
   */
  private renderQueryTab(): string {
    return `
      <div class="key-value-editor" id="query-editor">
        <div class="editor-header">
          <h4>Query Parameters</h4>
          <p class="text-secondary">设置 URL 查询参数</p>
        </div>
        <div class="key-value-rows" id="query-rows">
          ${this.renderKeyValueRows(this.currentConfig.queryParams)}
        </div>
        <button class="add-row-btn" onclick="this.addQueryRow()">
          ➕ Add Parameter
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Body Tab
   */
  private renderBodyTab(): string {
    return `
      <div class="body-editor" id="body-editor">
        <div class="editor-header">
          <h4>Request Body</h4>
          <div class="body-type-selector">
            <select id="body-type-select" class="form-control">
              <option value="raw" ${this.currentConfig.body.type === 'raw' ? 'selected' : ''}>Raw</option>
              <option value="json" ${this.currentConfig.body.type === 'json' ? 'selected' : ''}>JSON</option>
              <option value="form-data" ${this.currentConfig.body.type === 'form-data' ? 'selected' : ''}>Form Data</option>
              <option value="x-www-form-urlencoded" ${this.currentConfig.body.type === 'x-www-form-urlencoded' ? 'selected' : ''}>URL Encoded</option>
            </select>
          </div>
        </div>
        
        <div class="body-content" id="body-content">
          ${this.renderBodyContent()}
        </div>
      </div>
    `;
  }

  /**
   * 渲染 Auth Tab
   */
  private renderAuthTab(): string {
    const authType = this.currentConfig.auth?.type || 'none';
    return `
      <div class="auth-editor" id="auth-editor">
        <div class="editor-header">
          <h4>Authentication</h4>
          <div class="auth-type-selector">
            <select id="auth-type-select" class="form-control">
              <option value="none" ${authType === 'none' ? 'selected' : ''}>No Auth</option>
              <option value="basic" ${authType === 'basic' ? 'selected' : ''}>Basic Auth</option>
              <option value="bearer" ${authType === 'bearer' ? 'selected' : ''}>Bearer Token</option>
              <option value="custom" ${authType === 'custom' ? 'selected' : ''}>Custom Header</option>
            </select>
          </div>
        </div>
        
        <div class="auth-content" id="auth-content">
          ${this.renderAuthContent()}
        </div>
      </div>
    `;
  }

  /**
   * 渲染 cURL Tab
   */
  private renderCurlTab(): string {
    return `
      <div class="curl-editor" id="curl-editor">
        <!-- cURL 解析器组件将在这里渲染 -->
      </div>
    `;
  }

  /**
   * 渲染键值对行
   */
  private renderKeyValueRows(data: Record<string, string>): string {
    const rows = Object.entries(data).map(([key, value]) => 
      this.renderKeyValueRow(key, value)
    ).join('');
    
    // 添加一个空行用于新增
    return rows + this.renderKeyValueRow('', '');
  }

  /**
   * 渲染单个键值对行
   */
  private renderKeyValueRow(key: string = '', value: string = ''): string {
    return `
      <div class="key-value-row">
        <input 
          type="text" 
          class="form-control key-value-input" 
          placeholder="Key" 
          value="${key}"
          data-type="key"
        />
        <input 
          type="text" 
          class="form-control key-value-input" 
          placeholder="Value" 
          value="${value}"
          data-type="value"
        />
        <button class="remove-btn" title="Remove">
          🗑️
        </button>
      </div>
    `;
  }

  /**
   * 渲染 Body 内容
   */
  private renderBodyContent(): string {
    const bodyType = this.currentConfig.body.type;
    
    if (bodyType === 'raw' || bodyType === 'json') {
      return `
        <div class="json-editor">
          <textarea 
            id="body-textarea" 
            class="json-textarea" 
            placeholder="${bodyType === 'json' ? 'Enter JSON data...' : 'Enter raw data...'}"
            rows="8"
          >${this.currentConfig.body.content}</textarea>
          <div id="body-error" class="json-error" style="display: none;"></div>
        </div>
      `;
    } else {
      return `
        <div class="key-value-editor" id="body-form-editor">
          <div class="key-value-rows" id="body-form-rows">
            ${this.renderFormDataRows()}
          </div>
          <button class="add-row-btn" onclick="this.addBodyFormRow()">
            ➕ Add Field
          </button>
        </div>
      `;
    }
  }

  /**
   * 渲染 Auth 内容
   */
  private renderAuthContent(): string {
    const authType = this.currentConfig.auth?.type || 'none';
    
    if (authType === 'none') {
      return '<p class="text-secondary">No authentication required.</p>';
    }
    
    const credentials = this.currentConfig.auth?.credentials || {};
    
    switch (authType) {
      case 'basic':
        return `
          <div class="auth-basic">
            <div class="form-group">
              <label class="form-label">Username</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-username"
                value="${credentials.username || ''}"
                placeholder="Enter username"
              />
            </div>
            <div class="form-group">
              <label class="form-label">Password</label>
              <input 
                type="password" 
                class="form-control" 
                id="auth-password"
                value="${credentials.password || ''}"
                placeholder="Enter password"
              />
            </div>
          </div>
        `;
      
      case 'bearer':
        return `
          <div class="auth-bearer">
            <div class="form-group">
              <label class="form-label">Token</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-token"
                value="${credentials.token || ''}"
                placeholder="Enter bearer token"
              />
            </div>
          </div>
        `;
      
      case 'custom':
        return `
          <div class="auth-custom">
            <div class="form-group">
              <label class="form-label">Header Name</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-header-name"
                value="${credentials.headerName || ''}"
                placeholder="e.g., X-API-Key"
              />
            </div>
            <div class="form-group">
              <label class="form-label">Header Value</label>
              <input 
                type="text" 
                class="form-control" 
                id="auth-header-value"
                value="${credentials.headerValue || ''}"
                placeholder="Enter header value"
              />
            </div>
          </div>
        `;
      
      default:
        return '<p class="text-secondary">Unknown authentication type.</p>';
    }
  }

  /**
   * 渲染表单数据行
   */
  private renderFormDataRows(): string {
    // 这里需要解析 body.content 中的表单数据
    // 暂时返回空行
    return this.renderKeyValueRow('', '');
  }

  /**
   * 切换 Tab
   */
  private switchTab(tabName: string): void {
    // 更新按钮状态
    this.tabButtons.forEach((button, name) => {
      if (name === tabName) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });

    // 更新内容显示
    this.tabContents.forEach((content, name) => {
      if (name === tabName) {
        content.style.display = 'block';
      } else {
        content.style.display = 'none';
      }
    });

    this.activeTab = tabName;
  }

  /**
   * 处理对比按钮点击
   */
  private handleCompareClick(): void {
    const validation = this.validate();
    if (validation.isValid) {
      // 触发对比事件
      document.dispatchEvent(new CustomEvent('request-compare', {
        detail: this.currentConfig
      }));
    }
  }

  /**
   * 验证配置并通知
   */
  private validateAndNotify(): void {
    const validation = this.validate();
    
    // 更新对比按钮状态
    const compareBtn = this.container.querySelector('#compare-btn') as HTMLButtonElement;
    if (compareBtn) {
      compareBtn.disabled = !validation.isValid;
    }

    // 通知配置变化
    this.onConfigChange?.(this.currentConfig);
    this.onValidationChange?.(validation.isValid, validation.errors);
  }

  /**
   * 验证当前配置
   */
  private validate(): ValidationResult {
    return ValidationUtils.validateRequestConfig(this.currentConfig);
  }

  /**
   * 验证单个字段并显示错误
   */
  private validateField(fieldName: string, element: HTMLElement): void {
    let value: any;
    let error: string | null = null;

    // 获取字段值
    switch (fieldName) {
      case 'oldUrl':
      case 'newUrl':
        value = (element as HTMLInputElement).value;
        error = ValidationUtils.validateField(fieldName, value);
        break;
      case 'json':
        value = (element as HTMLTextAreaElement).value;
        error = ValidationUtils.validateField('json', value);
        break;
      case 'headers':
        // 这里需要收集所有 headers 数据
        value = this.collectKeyValueData('headers');
        error = ValidationUtils.validateField('headers', value);
        break;
      case 'queryParams':
        // 这里需要收集所有 query 数据
        value = this.collectKeyValueData('query');
        error = ValidationUtils.validateField('queryParams', value);
        break;
    }

    // 更新元素的验证状态
    this.updateFieldValidationState(element, error);
  }

  /**
   * 更新字段的验证状态
   */
  private updateFieldValidationState(element: HTMLElement, error: string | null): void {
    // 移除之前的验证类
    element.classList.remove('is-valid', 'is-invalid');

    // 添加新的验证类
    if (error) {
      element.classList.add('is-invalid');
      this.showFieldError(element, error);
    } else {
      element.classList.add('is-valid');
      this.hideFieldError(element);
    }
  }

  /**
   * 显示字段错误
   */
  private showFieldError(element: HTMLElement, error: string): void {
    // 查找或创建错误提示元素
    let errorElement = element.parentElement?.querySelector('.invalid-feedback') as HTMLElement;

    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'invalid-feedback';
      element.parentElement?.appendChild(errorElement);
    }

    errorElement.textContent = error;
    errorElement.style.display = 'block';
  }

  /**
   * 隐藏字段错误
   */
  private hideFieldError(element: HTMLElement): void {
    const errorElement = element.parentElement?.querySelector('.invalid-feedback') as HTMLElement;
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  /**
   * 收集键值对数据
   */
  private collectKeyValueData(type: 'headers' | 'query'): Record<string, string> {
    const container = this.container.querySelector(`#${type}-rows`);
    const data: Record<string, string> = {};

    if (container) {
      const rows = container.querySelectorAll('.key-value-row');
      rows.forEach(row => {
        const keyInput = row.querySelector('[data-type="key"]') as HTMLInputElement;
        const valueInput = row.querySelector('[data-type="value"]') as HTMLInputElement;

        if (keyInput && valueInput && keyInput.value.trim()) {
          data[keyInput.value.trim()] = valueInput.value;
        }
      });
    }

    return data;
  }

  /**
   * 更新键值对数据
   */
  private updateKeyValueData(type: 'headers' | 'query'): void {
    const data = this.collectKeyValueData(type);

    if (type === 'headers') {
      this.currentConfig.headers = data;
    } else {
      this.currentConfig.queryParams = data;
    }
  }

  /**
   * 添加键值对行
   */
  private addKeyValueRow(type: 'headers' | 'query'): void {
    const container = this.container.querySelector(`#${type}-rows`);
    if (container) {
      const newRow = document.createElement('div');
      newRow.innerHTML = this.renderKeyValueRow('', '');
      container.appendChild(newRow.firstElementChild!);
    }
  }

  /**
   * 更新 Body 内容
   */
  private updateBodyContent(): void {
    const bodyContent = this.container.querySelector('#body-content');
    if (bodyContent) {
      bodyContent.innerHTML = this.renderBodyContent();
      this.bindBodyEvents(); // 重新绑定事件
    }
  }

  /**
   * 更新 Auth 内容
   */
  private updateAuthContent(): void {
    const authContent = this.container.querySelector('#auth-content');
    if (authContent) {
      authContent.innerHTML = this.renderAuthContent();
      this.bindAuthEvents(); // 重新绑定事件
    }
  }

  /**
   * 处理对比按钮点击
   */
  private handleCompareClick(): void {
    const validation = this.validate();
    if (validation.isValid) {
      // 触发对比事件
      document.dispatchEvent(new CustomEvent('request-compare', {
        detail: this.currentConfig
      }));
    } else {
      // 显示验证错误
      console.warn('Configuration validation failed:', validation.errors);
    }
  }

  /**
   * 处理 cURL 配置解析完成
   */
  private handleCurlConfigParsed(parsedConfig: RequestConfig): void {
    // 合并解析的配置到当前配置
    this.mergeConfig(parsedConfig);

    // 重新渲染界面以反映新配置
    this.render();
    this.bindEvents();

    // 切换到 Headers tab 以显示导入的配置
    this.switchTab('headers');

    // 验证并通知配置变化
    this.validateAndNotify();

    // 显示成功提示
    this.showImportSuccessNotification();
  }

  /**
   * 合并配置
   */
  private mergeConfig(parsedConfig: Partial<RequestConfig>): void {
    // 更新基本配置
    if (parsedConfig.method) {
      this.currentConfig.method = parsedConfig.method;
    }

    if (parsedConfig.oldUrl) {
      this.currentConfig.oldUrl = parsedConfig.oldUrl;
      // 如果新 URL 为空，也设置为相同的 URL
      if (!this.currentConfig.newUrl) {
        this.currentConfig.newUrl = parsedConfig.oldUrl;
      }
    }

    // 合并请求头
    if (parsedConfig.headers) {
      this.currentConfig.headers = {
        ...this.currentConfig.headers,
        ...parsedConfig.headers
      };
    }

    // 合并查询参数
    if (parsedConfig.queryParams) {
      this.currentConfig.queryParams = {
        ...this.currentConfig.queryParams,
        ...parsedConfig.queryParams
      };
    }

    // 更新请求体
    if (parsedConfig.body) {
      this.currentConfig.body = {
        ...this.currentConfig.body,
        ...parsedConfig.body
      };
    }

    // 更新认证信息
    if (parsedConfig.auth) {
      this.currentConfig.auth = parsedConfig.auth;
    }
  }

  /**
   * 显示导入成功通知
   */
  private showImportSuccessNotification(): void {
    // 创建临时通知元素
    const notification = document.createElement('div');
    notification.className = 'import-success-notification';
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">✅</span>
        <span class="notification-text">cURL configuration imported successfully!</span>
      </div>
    `;

    // 添加到页面
    this.container.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // 自动移除
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }



  /**
   * 获取默认配置
   */
  private getDefaultConfig(): RequestConfig {
    return {
      method: 'GET',
      oldUrl: '',
      newUrl: '',
      headers: {
        'Content-Type': 'application/json'
      },
      queryParams: {},
      body: {
        type: 'json',
        content: ''
      },
      timeout: 10000,
      ignoreFields: ['timestamp', 'traceId']
    };
  }

  /**
   * 公共方法：获取当前配置
   */
  public getRequestConfig(): RequestConfig {
    return { ...this.currentConfig };
  }

  /**
   * 公共方法：设置配置
   */
  public setRequestConfig(config: RequestConfig): void {
    this.currentConfig = { ...config };
    this.render();
    this.bindEvents();
    this.validateAndNotify();
  }

  /**
   * 公共方法：设置事件回调
   */
  public setCallbacks(callbacks: {
    onConfigChange?: (config: RequestConfig) => void;
    onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
  }): void {
    this.onConfigChange = callbacks.onConfigChange;
    this.onValidationChange = callbacks.onValidationChange;
  }
}
