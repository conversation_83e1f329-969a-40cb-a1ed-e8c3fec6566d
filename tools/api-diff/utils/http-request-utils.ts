/**
 * HTTP Request Utils
 * 增强的 HTTP 请求工具，基于 BaseTool 提供更丰富的功能
 */

import type { 
  RequestConfig, 
  ApiResponse, 
  HttpMethod 
} from '../types/api-diff-types';

/**
 * HTTP 请求选项接口
 */
export interface HttpRequestOptions {
  method: HttpMethod;
  url: string;
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
  credentials?: RequestCredentials;
  signal?: AbortSignal;
}

/**
 * 请求执行结果接口
 */
export interface RequestExecutionResult {
  response: ApiResponse | null;
  error: string | null;
  duration: number;
  startTime: number;
  endTime: number;
}

/**
 * HTTP 请求工具类
 */
export class HttpRequestUtils {
  
  /**
   * 执行 HTTP 请求
   */
  static async executeRequest(options: HttpRequestOptions): Promise<RequestExecutionResult> {
    const startTime = Date.now();
    let response: ApiResponse | null = null;
    let error: string | null = null;
    
    try {
      // 构建 fetch 选项
      const fetchOptions = this.buildFetchOptions(options);
      
      // 执行请求
      const fetchResponse = await fetch(options.url, fetchOptions);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 构建响应对象
      response = await this.buildApiResponse(fetchResponse, startTime, duration);
      
      return {
        response,
        error: null,
        duration,
        startTime,
        endTime
      };
      
    } catch (err) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      error = this.formatError(err);
      
      return {
        response: null,
        error,
        duration,
        startTime,
        endTime
      };
    }
  }

  /**
   * 从 RequestConfig 创建请求选项
   */
  static createRequestOptions(config: RequestConfig, url: string): HttpRequestOptions {
    const options: HttpRequestOptions = {
      method: config.method,
      url: url,
      headers: { ...config.headers },
      timeout: config.timeout || 10000
    };

    // 处理查询参数
    if (config.queryParams && Object.keys(config.queryParams).length > 0) {
      const urlObj = new URL(url);
      Object.entries(config.queryParams).forEach(([key, value]) => {
        if (value) {
          urlObj.searchParams.set(key, value);
        }
      });
      options.url = urlObj.toString();
    }

    // 处理请求体
    if (config.body && config.body.content.trim()) {
      options.body = this.formatRequestBody(config.body.content, config.body.type);
      
      // 自动设置 Content-Type（如果未设置）
      if (!options.headers!['Content-Type'] && !options.headers!['content-type']) {
        options.headers!['Content-Type'] = this.getContentType(config.body.type);
      }
    }

    // 处理认证
    if (config.auth) {
      this.applyAuthentication(options, config.auth);
    }

    return options;
  }

  /**
   * 构建 fetch 选项
   */
  private static buildFetchOptions(options: HttpRequestOptions): RequestInit {
    const fetchOptions: RequestInit = {
      method: options.method,
      headers: options.headers || {},
      credentials: options.credentials || 'omit'
    };

    // 添加请求体（GET/HEAD 方法不应有 body）
    if (options.body && !['GET', 'HEAD'].includes(options.method)) {
      fetchOptions.body = options.body;
    }

    // 添加信号（用于超时和取消）
    if (options.signal) {
      fetchOptions.signal = options.signal;
    }

    return fetchOptions;
  }

  /**
   * 构建 API 响应对象
   */
  private static async buildApiResponse(
    fetchResponse: Response, 
    startTime: number, 
    duration: number
  ): Promise<ApiResponse> {
    // 获取响应头
    const headers: Record<string, string> = {};
    fetchResponse.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // 获取响应体文本
    const bodyText = await fetchResponse.text();
    
    // 尝试解析 JSON
    let body: any = bodyText;
    let isJson = false;
    
    if (bodyText.trim()) {
      try {
        body = JSON.parse(bodyText);
        isJson = true;
      } catch (e) {
        // 保持为文本
        isJson = false;
      }
    }

    return {
      status: fetchResponse.status,
      statusText: fetchResponse.statusText,
      headers,
      body,
      bodyText,
      isJson,
      duration,
      timestamp: startTime,
      error: fetchResponse.ok ? undefined : `HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`
    };
  }

  /**
   * 格式化请求体
   */
  private static formatRequestBody(content: string, type: string): string {
    switch (type) {
      case 'json':
        try {
          // 验证并格式化 JSON
          const parsed = JSON.parse(content);
          return JSON.stringify(parsed);
        } catch (e) {
          return content; // 如果解析失败，返回原始内容
        }
      
      case 'x-www-form-urlencoded':
        // 确保是正确的 URL 编码格式
        return content;
      
      case 'form-data':
        // 表单数据处理（这里简化处理）
        return content;
      
      default:
        return content;
    }
  }

  /**
   * 获取内容类型
   */
  private static getContentType(bodyType: string): string {
    switch (bodyType) {
      case 'json':
        return 'application/json';
      case 'x-www-form-urlencoded':
        return 'application/x-www-form-urlencoded';
      case 'form-data':
        return 'multipart/form-data';
      default:
        return 'text/plain';
    }
  }

  /**
   * 应用认证
   */
  private static applyAuthentication(
    options: HttpRequestOptions, 
    auth: RequestConfig['auth']
  ): void {
    if (!auth || !options.headers) return;

    switch (auth.type) {
      case 'basic':
        if (auth.credentials.username && auth.credentials.password) {
          const credentials = btoa(`${auth.credentials.username}:${auth.credentials.password}`);
          options.headers['Authorization'] = `Basic ${credentials}`;
        }
        break;
        
      case 'bearer':
        if (auth.credentials.token) {
          options.headers['Authorization'] = `Bearer ${auth.credentials.token}`;
        }
        break;
        
      case 'custom':
        if (auth.credentials.headerName && auth.credentials.headerValue) {
          options.headers[auth.credentials.headerName] = auth.credentials.headerValue;
        }
        break;
    }
  }

  /**
   * 格式化错误信息
   */
  private static formatError(error: any): string {
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return 'Network error: Unable to connect to the server';
    }
    
    if (error instanceof DOMException && error.name === 'AbortError') {
      return 'Request timeout or cancelled';
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    return String(error);
  }

  /**
   * 创建超时信号
   */
  static createTimeoutSignal(timeout: number): AbortSignal {
    const controller = new AbortController();
    
    setTimeout(() => {
      controller.abort();
    }, timeout);
    
    return controller.signal;
  }

  /**
   * 创建可取消的请求
   */
  static createCancellableRequest(options: HttpRequestOptions): {
    execute: () => Promise<RequestExecutionResult>;
    cancel: () => void;
  } {
    const controller = new AbortController();
    
    const execute = async (): Promise<RequestExecutionResult> => {
      const requestOptions = {
        ...options,
        signal: controller.signal
      };
      
      // 如果设置了超时，创建超时信号
      if (options.timeout) {
        const timeoutSignal = this.createTimeoutSignal(options.timeout);
        
        // 合并信号
        const combinedSignal = this.combineSignals([controller.signal, timeoutSignal]);
        requestOptions.signal = combinedSignal;
      }
      
      return this.executeRequest(requestOptions);
    };
    
    const cancel = (): void => {
      controller.abort();
    };
    
    return { execute, cancel };
  }

  /**
   * 合并多个 AbortSignal
   */
  private static combineSignals(signals: AbortSignal[]): AbortSignal {
    const controller = new AbortController();
    
    signals.forEach(signal => {
      if (signal.aborted) {
        controller.abort();
        return;
      }
      
      signal.addEventListener('abort', () => {
        controller.abort();
      });
    });
    
    return controller.signal;
  }

  /**
   * 验证 URL
   */
  static validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取错误类型
   */
  static getErrorType(error: string): 'network' | 'timeout' | 'http' | 'unknown' {
    if (error.includes('Network error') || error.includes('fetch')) {
      return 'network';
    }
    
    if (error.includes('timeout') || error.includes('cancelled')) {
      return 'timeout';
    }
    
    if (error.includes('HTTP')) {
      return 'http';
    }
    
    return 'unknown';
  }

  /**
   * 格式化响应大小
   */
  static formatResponseSize(bodyText: string): string {
    const bytes = new Blob([bodyText]).size;
    
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  }

  /**
   * 获取响应状态描述
   */
  static getStatusDescription(status: number): string {
    if (status >= 200 && status < 300) {
      return 'Success';
    } else if (status >= 300 && status < 400) {
      return 'Redirect';
    } else if (status >= 400 && status < 500) {
      return 'Client Error';
    } else if (status >= 500) {
      return 'Server Error';
    } else {
      return 'Unknown';
    }
  }
}
