/**
 * cURL Parsing Utils
 * cURL 命令解析工具，支持常见 cURL 参数解析
 */

import type { 
  RequestConfig, 
  ParseResult, 
  HttpMethod, 
  BodyType 
} from '../types/api-diff-types';

/**
 * cURL 解析器类
 */
export class CurlParsingUtils {
  
  /**
   * 解析 cURL 命令
   */
  static parse(curlCommand: string): ParseResult {
    try {
      const trimmedCommand = curlCommand.trim();
      
      if (!trimmedCommand) {
        return {
          success: false,
          error: 'cURL command is empty'
        };
      }

      // 检查是否以 curl 开头
      if (!trimmedCommand.toLowerCase().startsWith('curl')) {
        return {
          success: false,
          error: 'Command must start with "curl"'
        };
      }

      // 解析命令
      const config = this.parseCommand(trimmedCommand);
      
      return {
        success: true,
        config
      };
      
    } catch (error) {
      return {
        success: false,
        error: `Parse error: ${error.message}`,
        errorPosition: 0
      };
    }
  }

  /**
   * 解析 cURL 命令的核心逻辑
   */
  private static parseCommand(command: string): Partial<RequestConfig> {
    const config: Partial<RequestConfig> = {
      method: 'GET',
      headers: {},
      queryParams: {},
      body: {
        type: 'raw',
        content: ''
      }
    };

    // 分词处理，考虑引号和转义
    const tokens = this.tokenize(command);
    
    let i = 1; // 跳过 'curl'
    let url = '';

    while (i < tokens.length) {
      const token = tokens[i];

      if (token.startsWith('-')) {
        const result = this.parseOption(token, tokens, i, config);
        i = result.nextIndex;
      } else if (!url && this.isUrl(token)) {
        // 第一个看起来像 URL 的参数作为 URL
        url = token;
        i++;
      } else {
        // 跳过未识别的参数
        i++;
      }
    }

    // 设置 URL
    if (url) {
      config.oldUrl = url;
      config.newUrl = url; // 默认新旧 URL 相同
    }

    return config;
  }

  /**
   * 分词处理，正确处理引号和转义
   */
  private static tokenize(command: string): string[] {
    const tokens: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let escaped = false;

    for (let i = 0; i < command.length; i++) {
      const char = command[i];

      if (escaped) {
        current += char;
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      if (!inQuotes && (char === '"' || char === "'")) {
        inQuotes = true;
        quoteChar = char;
        continue;
      }

      if (inQuotes && char === quoteChar) {
        inQuotes = false;
        quoteChar = '';
        continue;
      }

      if (!inQuotes && /\s/.test(char)) {
        if (current) {
          tokens.push(current);
          current = '';
        }
        continue;
      }

      current += char;
    }

    if (current) {
      tokens.push(current);
    }

    return tokens;
  }

  /**
   * 解析选项参数
   */
  private static parseOption(
    option: string, 
    tokens: string[], 
    currentIndex: number, 
    config: Partial<RequestConfig>
  ): { nextIndex: number } {
    
    switch (option) {
      case '-X':
      case '--request':
        return this.parseMethod(tokens, currentIndex, config);
        
      case '-H':
      case '--header':
        return this.parseHeader(tokens, currentIndex, config);
        
      case '-d':
      case '--data':
      case '--data-raw':
      case '--data-binary':
        return this.parseData(tokens, currentIndex, config, 'raw');
        
      case '--data-urlencode':
        return this.parseData(tokens, currentIndex, config, 'x-www-form-urlencoded');
        
      case '-u':
      case '--user':
        return this.parseAuth(tokens, currentIndex, config);
        
      case '--compressed':
        // 添加 Accept-Encoding 头
        if (!config.headers) config.headers = {};
        config.headers['Accept-Encoding'] = 'gzip, deflate';
        return { nextIndex: currentIndex + 1 };
        
      case '--insecure':
      case '-k':
        // 忽略 SSL 证书验证（在浏览器环境中无法控制）
        return { nextIndex: currentIndex + 1 };
        
      case '-L':
      case '--location':
        // 跟随重定向（浏览器默认行为）
        return { nextIndex: currentIndex + 1 };
        
      case '-v':
      case '--verbose':
        // 详细输出（忽略）
        return { nextIndex: currentIndex + 1 };
        
      case '-s':
      case '--silent':
        // 静默模式（忽略）
        return { nextIndex: currentIndex + 1 };
        
      default:
        // 跳过未知选项
        if (currentIndex + 1 < tokens.length && !tokens[currentIndex + 1].startsWith('-')) {
          // 如果下一个 token 不是选项，跳过它（可能是参数值）
          return { nextIndex: currentIndex + 2 };
        }
        return { nextIndex: currentIndex + 1 };
    }
  }

  /**
   * 解析 HTTP 方法
   */
  private static parseMethod(
    tokens: string[], 
    currentIndex: number, 
    config: Partial<RequestConfig>
  ): { nextIndex: number } {
    if (currentIndex + 1 < tokens.length) {
      const method = tokens[currentIndex + 1].toUpperCase() as HttpMethod;
      if (['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'].includes(method)) {
        config.method = method;
      }
      return { nextIndex: currentIndex + 2 };
    }
    return { nextIndex: currentIndex + 1 };
  }

  /**
   * 解析请求头
   */
  private static parseHeader(
    tokens: string[], 
    currentIndex: number, 
    config: Partial<RequestConfig>
  ): { nextIndex: number } {
    if (currentIndex + 1 < tokens.length) {
      const headerString = tokens[currentIndex + 1];
      const colonIndex = headerString.indexOf(':');
      
      if (colonIndex > 0) {
        const name = headerString.substring(0, colonIndex).trim();
        const value = headerString.substring(colonIndex + 1).trim();
        
        if (!config.headers) config.headers = {};
        config.headers[name] = value;
      }
      
      return { nextIndex: currentIndex + 2 };
    }
    return { nextIndex: currentIndex + 1 };
  }

  /**
   * 解析请求数据
   */
  private static parseData(
    tokens: string[], 
    currentIndex: number, 
    config: Partial<RequestConfig>,
    bodyType: BodyType
  ): { nextIndex: number } {
    if (currentIndex + 1 < tokens.length) {
      const data = tokens[currentIndex + 1];
      
      if (!config.body) {
        config.body = { type: bodyType, content: '' };
      }
      
      // 如果已有数据，追加（用 & 连接）
      if (config.body.content) {
        config.body.content += '&' + data;
      } else {
        config.body.content = data;
      }
      
      config.body.type = bodyType;
      
      // 如果是 JSON 数据，尝试格式化
      if (bodyType === 'raw' && this.isJsonString(data)) {
        config.body.type = 'json';
        try {
          // 格式化 JSON
          const parsed = JSON.parse(data);
          config.body.content = JSON.stringify(parsed, null, 2);
        } catch (e) {
          // 保持原始数据
        }
      }
      
      // 自动设置 Content-Type
      if (!config.headers) config.headers = {};
      if (!config.headers['Content-Type']) {
        switch (bodyType) {
          case 'json':
            config.headers['Content-Type'] = 'application/json';
            break;
          case 'x-www-form-urlencoded':
            config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
            break;
          case 'form-data':
            config.headers['Content-Type'] = 'multipart/form-data';
            break;
        }
      }
      
      return { nextIndex: currentIndex + 2 };
    }
    return { nextIndex: currentIndex + 1 };
  }

  /**
   * 解析认证信息
   */
  private static parseAuth(
    tokens: string[], 
    currentIndex: number, 
    config: Partial<RequestConfig>
  ): { nextIndex: number } {
    if (currentIndex + 1 < tokens.length) {
      const authString = tokens[currentIndex + 1];
      const colonIndex = authString.indexOf(':');
      
      if (colonIndex > 0) {
        const username = authString.substring(0, colonIndex);
        const password = authString.substring(colonIndex + 1);
        
        config.auth = {
          type: 'basic',
          credentials: {
            username,
            password
          }
        };
      }
      
      return { nextIndex: currentIndex + 2 };
    }
    return { nextIndex: currentIndex + 1 };
  }

  /**
   * 检查字符串是否是 URL
   */
  private static isUrl(str: string): boolean {
    try {
      new URL(str);
      return true;
    } catch {
      return str.startsWith('http://') || str.startsWith('https://');
    }
  }

  /**
   * 检查字符串是否是 JSON
   */
  private static isJsonString(str: string): boolean {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取支持的选项列表
   */
  static getSupportedOptions(): string[] {
    return [
      '-X, --request',
      '-H, --header',
      '-d, --data',
      '--data-raw',
      '--data-binary',
      '--data-urlencode',
      '-u, --user',
      '--compressed',
      '--insecure, -k',
      '-L, --location',
      '-v, --verbose',
      '-s, --silent'
    ];
  }

  /**
   * 验证 cURL 命令格式
   */
  static validateCurlCommand(command: string): string | null {
    const trimmed = command.trim();
    
    if (!trimmed) {
      return 'cURL command cannot be empty';
    }
    
    if (!trimmed.toLowerCase().startsWith('curl')) {
      return 'Command must start with "curl"';
    }
    
    // 检查是否包含 URL
    const tokens = this.tokenize(trimmed);
    const hasUrl = tokens.some((token, index) => 
      index > 0 && !token.startsWith('-') && this.isUrl(token)
    );
    
    if (!hasUrl) {
      return 'cURL command must contain a valid URL';
    }
    
    return null; // 验证通过
  }

  /**
   * 格式化解析错误信息
   */
  static formatParseError(error: string, position?: number): string {
    if (position !== undefined) {
      return `Parse error at position ${position}: ${error}`;
    }
    return `Parse error: ${error}`;
  }

  /**
   * 示例 cURL 命令
   */
  static getExampleCommands(): string[] {
    return [
      'curl -X GET https://api.example.com/users',
      'curl -X POST https://api.example.com/users -H "Content-Type: application/json" -d \'{"name":"John"}\'',
      'curl -X PUT https://api.example.com/users/1 -u username:password -d "name=John&email=<EMAIL>"',
      'curl -X DELETE https://api.example.com/users/1 -H "Authorization: Bearer token123"',
      'curl https://api.example.com/data --compressed -L'
    ];
  }
}
