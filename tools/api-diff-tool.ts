/**
 * API Diff Tool - 接口返回结果差异对比工具
 * 在新标签页中提供类似 Postman 的接口测试和双端对比功能
 */

import { BaseTool } from '../utils/tool-template';
import type {
  RequestConfig,
  DualExecutionResult,
  ComponentState,
  RequestExecutionState,
  ToolConfig as ApiDiffToolConfig
} from './api-diff/types/api-diff-types';

// 导入所有组件
import { RequestBuilder } from './api-diff/components/request-builder';
import { DualRequestExecutor } from './api-diff/components/dual-request-executor';
import { ResponseRenderer } from './api-diff/components/response-renderer';
import { DiffRenderer } from './api-diff/components/diff-renderer';
import { ConfigManager } from './api-diff/components/config-manager';

export class ApiDiffTool extends BaseTool {
  // 工具基本信息
  id = 'api-diff-tool';
  name = '接口 Diff 测试台';
  description = '页面版 Postman + 双端接口请求和可视化差异对比工具';
  icon = '🔄';
  categories = ['all', 'development', 'api'];
  version = { major: 1, minor: 0, patch: 0 };
  badge: 'new' = 'new';
  
  // 新标签页配置
  displayMode = 'newtab' as const;
  requiresFullscreen = true;
  
  // 传递给 newtab 的初始数据
  newtabData = {
    toolId: this.id,
    initialConfig: {
      theme: 'auto',
      layout: 'three-column',
      autoSave: true
    },
    permissions: ['storage', 'activeTab', 'clipboardWrite']
  };

  // 组件状态管理
  private componentStates: Map<string, ComponentState> = new Map();
  private requestExecutionState: RequestExecutionState = {
    executing: false,
    progress: 0,
    statusText: '就绪',
    cancellable: false
  };

  // 当前配置和响应数据
  private currentConfig: RequestConfig | null = null;
  private lastResponse: DualExecutionResult | null = null;

  // 组件实例
  private requestBuilder: RequestBuilder | null = null;
  private requestExecutor: DualRequestExecutor | null = null;
  private responseRenderer: ResponseRenderer | null = null;
  private diffRenderer: DiffRenderer | null = null;
  private configManager: ConfigManager | null = null;

  // DOM 容器引用
  private toolContainer: HTMLElement | null = null;
  private headerContainer: HTMLElement | null = null;
  private requestBuilderContainer: HTMLElement | null = null;
  private resultsContainer: HTMLElement | null = null;

  /**
   * 工具主入口方法
   * 在新标签页环境中初始化完整的工具界面
   */
  async action(): Promise<void> {
    try {
      console.log('🎬 API Diff Tool 开始执行');
      
      // 检查是否在 newtab 环境中
      if (!this.isInNewTabEnvironment()) {
        throw new Error('API Diff Tool 必须在新标签页环境中运行');
      }
      
      // 设置工具状态
      this.updateRequestExecutionState({
        executing: false,
        progress: 0,
        statusText: '初始化中...',
        cancellable: false
      });
      
      // 创建完整的工具界面
      await this.createToolInterface();
      
      // 初始化所有组件
      await this.initializeComponents();
      
      // 加载保存的配置
      await this.loadSavedConfiguration();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 更新状态为就绪
      this.updateRequestExecutionState({
        executing: false,
        progress: 100,
        statusText: '就绪',
        cancellable: false
      });
      
      console.log('✅ API Diff Tool 初始化完成');
      
    } catch (error) {
      console.error('❌ API Diff Tool 执行失败:', error);
      await this.showNotification('工具启动失败', `错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 新标签页环境初始化钩子
   */
  async onNewTabInit(): Promise<void> {
    console.log('🔧 API Diff Tool 在 New Tab 环境中初始化');
    
    try {
      // 加载工具专用样式
      await this.loadToolStyles();
      
      // 初始化全屏特定的资源
      await this.loadFullscreenResources();
      
      // 设置全局事件监听器
      this.setupGlobalEventListeners();
      
      // 初始化组件状态
      this.initializeComponentStates();
      
      console.log('✅ New Tab 环境初始化完成');
      
    } catch (error) {
      console.error('❌ New Tab 环境初始化失败:', error);
      throw error;
    }
  }

  /**
   * 新标签页环境销毁钩子
   */
  async onNewTabDestroy(): Promise<void> {
    console.log('🧹 API Diff Tool 在 New Tab 环境中清理');
    
    try {
      // 取消正在进行的请求
      await this.cancelCurrentRequests();
      
      // 清理资源
      await this.cleanupResources();
      
      // 移除事件监听器
      this.removeEventListeners();
      
      // 重置组件状态
      this.resetComponentStates();
      
      console.log('✅ New Tab 环境清理完成');
      
    } catch (error) {
      console.error('❌ New Tab 环境清理失败:', error);
    }
  }

  /**
   * 检查是否在新标签页环境中
   */
  private isInNewTabEnvironment(): boolean {
    return window.location.pathname.includes('newtab.html') || 
           window.location.pathname.includes('newtab/');
  }

  /**
   * 创建工具界面
   */
  private async createToolInterface(): Promise<void> {
    const container = document.getElementById('tool-container');
    if (!container) {
      throw new Error('找不到工具容器元素');
    }

    this.toolContainer = container;
    
    // 创建主界面结构
    container.innerHTML = `
      <div class="api-diff-tool">
        <!-- Header Bar -->
        <header id="header-container" class="tool-header">
          <div class="header-left">
            <h1 class="tool-title">
              <span class="tool-icon">${this.icon}</span>
              <span class="tool-name">${this.name}</span>
            </h1>
          </div>
          <div class="header-right">
            <div class="header-actions">
              <button id="history-btn" class="header-btn" title="查看历史配置">
                📋 History
              </button>
              <button id="import-btn" class="header-btn" title="导入配置">
                📥 Import
              </button>
              <button id="export-btn" class="header-btn" title="导出配置">
                📤 Export
              </button>
              <button id="settings-btn" class="header-btn" title="设置">
                ⚙️ Settings
              </button>
            </div>
          </div>
        </header>

        <!-- Request Builder Area -->
        <section id="request-builder-container" class="request-builder-area">
          <div class="request-builder-loading">
            <div class="loading-spinner"></div>
            <span>正在加载请求构建器...</span>
          </div>
        </section>

        <!-- Results Area -->
        <section id="results-container" class="results-area">
          <div class="results-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-icon">🚀</div>
              <h3>准备就绪</h3>
              <p>配置请求参数并点击"对比"按钮开始测试</p>
            </div>
          </div>
        </section>
      </div>
    `;

    // 获取容器引用
    this.headerContainer = document.getElementById('header-container');
    this.requestBuilderContainer = document.getElementById('request-builder-container');
    this.resultsContainer = document.getElementById('results-container');
  }

  /**
   * 初始化所有组件
   */
  private async initializeComponents(): Promise<void> {
    console.log('📦 正在初始化组件...');

    try {
      // 1. 初始化配置管理器
      this.setComponentState('config-manager', { loading: true, initialized: false });
      this.configManager = new ConfigManager();
      this.setComponentState('config-manager', { loading: false, initialized: true });

      // 2. 初始化请求构建器
      this.setComponentState('request-builder', { loading: true, initialized: false });
      if (!this.requestBuilderContainer) {
        throw new Error('请求构建器容器未找到');
      }

      this.requestBuilder = new RequestBuilder(this.requestBuilderContainer);
      this.requestBuilder.setCallbacks({
        onConfigChange: (config) => {
          this.currentConfig = config;
          this.handleConfigChange(config);
        },
        onValidationChange: (isValid, errors) => {
          this.handleValidationChange(isValid, errors);
        }
      });
      this.setComponentState('request-builder', { loading: false, initialized: true });

      // 3. 初始化请求执行器
      this.setComponentState('dual-request-executor', { loading: true, initialized: false });
      this.requestExecutor = new DualRequestExecutor();
      this.requestExecutor.addEventListener('status-change', (event) => {
        this.handleExecutionStatusChange(event.data.newStatus);
      });
      this.requestExecutor.addEventListener('completed', (event) => {
        this.handleExecutionCompleted(event.data);
      });
      this.requestExecutor.addEventListener('error', (event) => {
        this.handleExecutionError(event.data.error);
      });
      this.setComponentState('dual-request-executor', { loading: false, initialized: true });

      // 4. 初始化响应渲染器和差异渲染器
      this.setComponentState('response-renderer', { loading: true, initialized: false });
      this.setComponentState('diff-renderer', { loading: true, initialized: false });

      // 创建结果容器的子容器
      if (this.resultsContainer) {
        this.resultsContainer.innerHTML = `
          <div class="results-tabs">
            <button class="result-tab active" data-tab="response">响应对比</button>
            <button class="result-tab" data-tab="diff">差异分析</button>
          </div>
          <div class="results-content">
            <div id="response-panel" class="result-panel active"></div>
            <div id="diff-panel" class="result-panel"></div>
          </div>
        `;

        const responsePanel = document.getElementById('response-panel');
        const diffPanel = document.getElementById('diff-panel');

        if (responsePanel && diffPanel) {
          this.responseRenderer = new ResponseRenderer(responsePanel);
          this.diffRenderer = new DiffRenderer(diffPanel);
        }
      }

      this.setComponentState('response-renderer', { loading: false, initialized: true });
      this.setComponentState('diff-renderer', { loading: false, initialized: true });

      // 5. 绑定结果标签切换事件
      this.bindResultTabEvents();

      console.log('✅ 所有组件初始化完成');

    } catch (error) {
      console.error('❌ 组件初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载工具样式
   */
  private async loadToolStyles(): Promise<void> {
    // 检查样式是否已加载
    if (document.getElementById('api-diff-tool-styles')) {
      return;
    }

    // 创建样式链接
    const link = document.createElement('link');
    link.id = 'api-diff-tool-styles';
    link.rel = 'stylesheet';
    link.href = '/styles/api-diff-tool.css';
    
    document.head.appendChild(link);
    
    // 等待样式加载完成
    return new Promise((resolve, reject) => {
      link.onload = () => resolve();
      link.onerror = () => reject(new Error('样式加载失败'));
    });
  }

  /**
   * 加载全屏资源
   */
  private async loadFullscreenResources(): Promise<void> {
    // 这里可以加载大型资源、字体、图标等
    console.log('📦 加载全屏资源...');
  }

  /**
   * 设置全局事件监听器
   */
  private setupGlobalEventListeners(): void {
    // 键盘快捷键
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    
    // 窗口大小变化
    window.addEventListener('resize', this.handleWindowResize.bind(this));
    
    // 页面卸载前保存
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * 设置工具事件监听器
   */
  private setupEventListeners(): void {
    // Header 按钮事件
    this.headerContainer?.addEventListener('click', this.handleHeaderActions.bind(this));

    // 监听请求对比事件
    document.addEventListener('request-compare', (event: any) => {
      this.handleRequestCompare(event.detail);
    });
  }

  /**
   * 绑定结果标签切换事件
   */
  private bindResultTabEvents(): void {
    const resultTabs = document.querySelectorAll('.result-tab');
    resultTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tabName = target.getAttribute('data-tab');
        if (tabName) {
          this.switchResultTab(tabName);
        }
      });
    });
  }

  /**
   * 切换结果标签
   */
  private switchResultTab(tabName: string): void {
    // 更新标签状态
    const tabs = document.querySelectorAll('.result-tab');
    tabs.forEach(tab => {
      if (tab.getAttribute('data-tab') === tabName) {
        tab.classList.add('active');
      } else {
        tab.classList.remove('active');
      }
    });

    // 更新面板显示
    const panels = document.querySelectorAll('.result-panel');
    panels.forEach(panel => {
      if (panel.id === `${tabName}-panel`) {
        panel.classList.add('active');
      } else {
        panel.classList.remove('active');
      }
    });
  }

  /**
   * 处理 Header 按钮点击
   */
  private handleHeaderActions(event: Event): void {
    const target = event.target as HTMLElement;
    const buttonId = target.id;

    switch (buttonId) {
      case 'history-btn':
        this.showHistoryDialog();
        break;
      case 'import-btn':
        this.showImportDialog();
        break;
      case 'export-btn':
        this.showExportDialog();
        break;
      case 'settings-btn':
        this.showSettingsDialog();
        break;
    }
  }

  /**
   * 处理键盘快捷键
   */
  private handleKeyboardShortcuts(event: KeyboardEvent): void {
    // Ctrl/Cmd + Enter: 执行请求
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      // 这里将在后续任务中实现
      console.log('快捷键: 执行请求');
    }
    
    // Ctrl/Cmd + S: 保存配置
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      this.saveCurrentConfiguration();
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleWindowResize(): void {
    // 响应式布局调整
    console.log('窗口大小变化，调整布局');
  }

  /**
   * 处理页面卸载前事件
   */
  private handleBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.requestExecutionState.executing) {
      event.preventDefault();
      event.returnValue = '有请求正在执行中，确定要离开吗？';
    }
  }

  /**
   * 处理配置变化
   */
  private handleConfigChange(config: RequestConfig): void {
    this.currentConfig = config;
    console.log('配置已更新:', config);
  }

  /**
   * 处理验证状态变化
   */
  private handleValidationChange(isValid: boolean, errors: Record<string, string>): void {
    // 更新UI状态，显示验证错误等
    console.log('验证状态:', isValid, errors);
  }

  /**
   * 处理执行状态变化
   */
  private handleExecutionStatusChange(status: string): void {
    this.updateRequestExecutionState({
      statusText: `执行状态: ${status}`
    });
  }

  /**
   * 处理执行完成
   */
  private handleExecutionCompleted(result: DualExecutionResult): void {
    this.lastResponse = result;
    this.renderResults(result);

    // 保存执行历史
    if (this.configManager && this.currentConfig) {
      this.configManager.saveHistory('temp', result);
    }
  }

  /**
   * 处理执行错误
   */
  private handleExecutionError(error: string): void {
    this.updateRequestExecutionState({
      executing: false,
      statusText: `执行失败: ${error}`
    });
    this.showNotification('执行失败', error);
  }

  /**
   * 处理请求对比
   */
  private async handleRequestCompare(config: RequestConfig): Promise<void> {
    if (!this.requestExecutor || this.requestExecutionState.executing) {
      return;
    }

    try {
      this.updateRequestExecutionState({
        executing: true,
        progress: 0,
        statusText: '正在执行请求...',
        cancellable: true
      });

      const result = await this.requestExecutor.execute(config);
      this.handleExecutionCompleted(result);

    } catch (error) {
      this.handleExecutionError(error instanceof Error ? error.message : String(error));
    } finally {
      this.updateRequestExecutionState({
        executing: false,
        cancellable: false
      });
    }
  }

  /**
   * 渲染结果
   */
  private renderResults(result: DualExecutionResult): void {
    // 渲染响应对比
    if (this.responseRenderer) {
      this.responseRenderer.renderDualResponse(result);
    }

    // 渲染差异分析
    if (this.diffRenderer) {
      this.diffRenderer.renderDiff(result);
    }

    // 更新状态
    this.updateRequestExecutionState({
      statusText: `对比完成 - 耗时 ${result.totalDuration}ms`,
      progress: 100
    });
  }

  /**
   * 显示历史对话框
   */
  private async showHistoryDialog(): Promise<void> {
    if (!this.configManager) return;

    try {
      const histories = await this.configManager.getHistory();
      // 这里应该显示历史记录对话框
      console.log('历史记录:', histories);
      this.showNotification('信息', '历史记录功能开发中...');
    } catch (error) {
      this.showNotification('错误', '加载历史记录失败');
    }
  }

  /**
   * 显示导入对话框
   */
  private showImportDialog(): void {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && this.configManager) {
        try {
          const text = await file.text();
          const result = await this.configManager.importConfig(text);
          if (result.success) {
            this.showNotification('成功', '配置导入成功');
            // 重新加载配置到界面
            if (result.configId && this.requestBuilder) {
              const loadResult = await this.configManager.loadConfig(result.configId);
              if (loadResult.success && loadResult.config) {
                this.requestBuilder.setRequestConfig(loadResult.config);
              }
            }
          } else {
            this.showNotification('错误', result.error || '导入失败');
          }
        } catch (error) {
          this.showNotification('错误', '文件读取失败');
        }
      }
    };
    input.click();
  }

  /**
   * 显示导出对话框
   */
  private async showExportDialog(): Promise<void> {
    if (!this.configManager || !this.currentConfig) {
      this.showNotification('错误', '没有可导出的配置');
      return;
    }

    try {
      // 先保存当前配置
      const name = prompt('请输入配置名称:') || '未命名配置';
      const saveResult = await this.configManager.saveConfig(this.currentConfig, name);

      if (saveResult.success && saveResult.configId) {
        const exportResult = await this.configManager.exportConfig(saveResult.configId);
        if (exportResult.success && exportResult.data) {
          // 下载文件
          const blob = new Blob([exportResult.data], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${name}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          this.showNotification('成功', '配置导出成功');
        } else {
          this.showNotification('错误', exportResult.error || '导出失败');
        }
      } else {
        this.showNotification('错误', saveResult.error || '保存配置失败');
      }
    } catch (error) {
      this.showNotification('错误', '导出配置失败');
    }
  }

  /**
   * 显示设置对话框
   */
  private async showSettingsDialog(): Promise<void> {
    if (!this.configManager) return;

    try {
      const settings = await this.configManager.getSettings();
      // 这里应该显示设置对话框
      console.log('当前设置:', settings);
      this.showNotification('信息', '设置功能开发中...');
    } catch (error) {
      this.showNotification('错误', '加载设置失败');
    }
  }

  /**
   * 加载保存的配置
   */
  private async loadSavedConfiguration(): Promise<void> {
    if (!this.configManager || !this.requestBuilder) return;

    try {
      const configs = await this.configManager.getRecentConfigs(10);
      if (configs.length === 0) {
        this.showNotification('信息', '没有保存的配置');
        return;
      }

      // 简单实现：加载最近的配置
      // 实际应该显示配置选择对话框
      const config = configs[0];
      this.requestBuilder.setRequestConfig(config.config);
      this.showNotification('成功', `已加载配置: ${config.name}`);
    } catch (error) {
      this.showNotification('错误', '加载配置失败');
    }
  }

  /**
   * 保存当前配置
   */
  private async saveCurrentConfiguration(): Promise<void> {
    if (!this.configManager || !this.currentConfig) {
      this.showNotification('错误', '没有可保存的配置');
      return;
    }

    try {
      const name = prompt('请输入配置名称:');
      if (!name) return;

      const result = await this.configManager.saveConfig(this.currentConfig, name);
      if (result.success) {
        this.showNotification('成功', '配置保存成功');
      } else {
        this.showNotification('错误', result.error || '保存失败');
      }
    } catch (error) {
      this.showNotification('错误', '保存配置失败');
    }
  }

  /**
   * 取消当前请求
   */
  private async cancelCurrentRequests(): Promise<void> {
    if (this.requestExecutor && this.requestExecutor.canCancel()) {
      this.requestExecutor.cancel();
      this.updateRequestExecutionState({
        executing: false,
        cancellable: false,
        statusText: '请求已取消'
      });
      this.showNotification('信息', '请求已取消');
    }
  }

  /**
   * 清理资源
   */
  private async cleanupResources(): Promise<void> {
    // 取消正在进行的请求
    if (this.requestExecutor) {
      this.requestExecutor.cancel();
      this.requestExecutor.destroy();
    }

    // 清理配置管理器
    if (this.configManager) {
      this.configManager.destroy();
    }

    // 清理组件实例
    this.requestBuilder = null;
    this.requestExecutor = null;
    this.responseRenderer = null;
    this.diffRenderer = null;
    this.configManager = null;

    console.log('资源清理完成');
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    // 移除全局事件监听器
    document.removeEventListener('request-compare', this.handleRequestCompare.bind(this));
    console.log('事件监听器已移除');
  }

  /**
   * 显示通知
   */
  private showNotification(title: string, message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <div class="notification-title">${title}</div>
        <div class="notification-message">${message}</div>
      </div>
      <button class="notification-close">×</button>
    `;

    document.body.appendChild(notification);

    // 绑定关闭事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn?.addEventListener('click', () => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    });

    // 显示动画
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // 自动关闭
    setTimeout(() => {
      if (notification.parentNode) {
        notification.classList.remove('show');
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }
    }, 5000);
  }

  /**
   * 组件状态管理方法
   */
  private initializeComponentStates(): void {
    this.componentStates.clear();
  }

  private resetComponentStates(): void {
    this.componentStates.clear();
  }

  private setComponentState(componentId: string, state: Partial<ComponentState>): void {
    const currentState = this.componentStates.get(componentId) || {
      loading: false,
      initialized: false
    };
    
    this.componentStates.set(componentId, { ...currentState, ...state });
  }

  private getComponentState(componentId: string): ComponentState | null {
    return this.componentStates.get(componentId) || null;
  }

  /**
   * 请求执行状态管理
   */
  private updateRequestExecutionState(state: Partial<RequestExecutionState>): void {
    this.requestExecutionState = { ...this.requestExecutionState, ...state };
    
    // 触发状态更新事件
    document.dispatchEvent(new CustomEvent('request-execution-state-changed', {
      detail: this.requestExecutionState
    }));
  }

  /**
   * 获取当前请求执行状态
   */
  public getRequestExecutionState(): RequestExecutionState {
    return { ...this.requestExecutionState };
  }
}
