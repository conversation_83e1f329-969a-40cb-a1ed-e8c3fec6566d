import { toolRegistry } from '../../utils/tool-registry';
import { settingsManager } from '../../utils/settings-manager';
import { categoryManager } from '../../utils/category-manager';
import { notificationManager } from '../../utils/notification-manager';
import { styleManager } from '../../utils/style-manager';
import type { Tool, NewTabLaunchData } from '../../utils/tool-template';

class NewTabManager {
  private currentTool: Tool | null = null;
  private launchData: NewTabLaunchData | null = null;
  
  constructor() {
    this.init();
  }
  
  private async init() {
    try {
      console.log('🚀 New Tab Page初始化开始...');
      
      // 显示加载状态
      this.showLoading();
      
      // 初始化管理器
      await settingsManager.init();
      await toolRegistry.init();
      await categoryManager.init();
      
      // 加载样式
      await this.loadStyles();
      
      // 检查是否有启动数据
      await this.checkLaunchData();
      
      console.log('✅ New Tab Page初始化完成');
      
    } catch (error) {
      console.error('❌ New Tab Page初始化失败:', error);
      this.showError('初始化失败: ' + (error as Error).message);
    }
  }
  
  private async loadStyles() {
    try {
      // 注册和加载样式模块
      styleManager.registerModule({
        name: 'newtab',
        path: '/entrypoints/newtab/style.css',
        dependencies: ['design-tokens', 'components']
      });
      
      await styleManager.loadModule('newtab');
      console.log('✅ New Tab样式加载成功');
    } catch (error) {
      console.warn('❌ New Tab样式加载失败:', error);
    }
  }
  
  private async checkLaunchData() {
    try {
      const result = await browser.storage.local.get('newtab-tool-launch');
      const launchData = result['newtab-tool-launch'] as NewTabLaunchData;
      
      if (launchData) {
        console.log('📦 收到工具启动数据:', launchData);
        this.launchData = launchData;
        
        // 清理启动数据
        await browser.storage.local.remove('newtab-tool-launch');
        
        // 启动目标工具
        await this.launchTool(launchData.toolId, launchData.data);
      } else {
        console.log('📋 没有启动数据，显示工具选择界面');
        this.showToolSelector();
      }
    } catch (error) {
      console.error('❌ 检查启动数据失败:', error);
      this.showToolSelector();
    }
  }
  
  private async launchTool(toolId: string, data?: any) {
    try {
      console.log(`🎯 启动工具: ${toolId}`);
      
      const tool = toolRegistry.getById(toolId);
      if (!tool) {
        throw new Error(`工具 ${toolId} 不存在`);
      }
      
      this.currentTool = tool;
      
      // 检查工具是否支持newtab模式
      if (tool.displayMode !== 'newtab') {
        console.warn(`⚠️ 工具 ${toolId} 不支持newtab模式，但仍然尝试执行`);
      }
      
      // 调用newtab初始化钩子
      if (tool.onNewTabInit) {
        console.log(`🔧 执行工具 ${toolId} 的newtab初始化...`);
        await tool.onNewTabInit();
      }
      
      // 执行工具主逻辑
      if (typeof tool.action === 'function') {
        console.log(`🎬 执行工具 ${toolId} 的主逻辑...`);
        await tool.action();
        console.log(`✅ 工具 ${toolId} 执行完成`);
      } else {
        throw new Error(`工具 ${toolId} 的 action 方法不存在`);
      }
      
      // 隐藏加载状态，显示工具容器
      this.hideLoading();
      this.showToolContainer();
      
    } catch (error) {
      console.error(`❌ 启动工具 ${toolId} 失败:`, error);
      this.showError('工具启动失败: ' + (error as Error).message);
      this.showToolSelector();
    }
  }
  
  private showToolSelector() {
    console.log('📋 显示工具选择界面');
    
    // 隐藏加载状态和工具容器
    document.getElementById('loading')!.style.display = 'none';
    document.getElementById('tool-container')!.style.display = 'none';
    
    // 显示工具选择器
    const selector = document.getElementById('tool-selector')!;
    selector.style.display = 'block';
    
    // 渲染工具列表
    this.renderToolSelector();
  }
  
  private renderToolSelector() {
    const container = document.getElementById('selector-tools')!;
    const tools = toolRegistry.getEnabled();
    
    container.innerHTML = tools.map(tool => `
      <div class="tool-card" data-tool-id="${tool.id}">
        <span class="tool-icon">${tool.icon}</span>
        <div class="tool-info">
          <div class="tool-name">${tool.name}</div>
          <div class="tool-description">${tool.description}</div>
        </div>
        <div class="tool-actions">
          ${tool.displayMode === 'newtab' ? '<span class="badge newtab-badge">NewTab</span>' : ''}
          <button class="launch-btn" data-tool-id="${tool.id}">启动</button>
        </div>
      </div>
    `).join('');
    
    // 添加点击事件
    container.querySelectorAll('.launch-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const toolId = (e.target as HTMLElement).dataset.toolId;
        if (toolId) {
          this.launchTool(toolId);
        }
      });
    });
    
    // 搜索功能
    const searchInput = document.getElementById('selector-search') as HTMLInputElement;
    searchInput.addEventListener('input', (e) => {
      const query = (e.target as HTMLInputElement).value.toLowerCase();
      this.filterTools(query);
    });
  }
  
  private filterTools(query: string) {
    const cards = document.querySelectorAll('#selector-tools .tool-card');
    cards.forEach(card => {
      const toolName = card.querySelector('.tool-name')!.textContent!.toLowerCase();
      const toolDesc = card.querySelector('.tool-description')!.textContent!.toLowerCase();
      const matches = toolName.includes(query) || toolDesc.includes(query);
      (card as HTMLElement).style.display = matches ? 'flex' : 'none';
    });
  }
  
  private showLoading() {
    document.getElementById('loading')!.style.display = 'flex';
    document.getElementById('tool-container')!.style.display = 'none';
    document.getElementById('tool-selector')!.style.display = 'none';
  }
  
  private hideLoading() {
    document.getElementById('loading')!.style.display = 'none';
  }
  
  private showToolContainer() {
    document.getElementById('tool-container')!.style.display = 'block';
  }
  
  private showError(message: string) {
    const container = document.getElementById('tool-container')!;
    container.innerHTML = `
      <div class="error-message">
        <div class="error-icon">❌</div>
        <div class="error-text">${message}</div>
        <button class="retry-btn" onclick="location.reload()">重试</button>
        <button class="back-btn" onclick="this.showToolSelector()">返回工具选择</button>
      </div>
    `;
    
    this.hideLoading();
    this.showToolContainer();
  }
}

// 启动New Tab管理器
new NewTabManager();
